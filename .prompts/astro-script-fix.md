I need to check for best practice usage of my scripts loaded on my booking.astro page. 

Use firecrawl and fetch to get details and usage from these sites. Solely use the filesystem ADVANCED MCP SERVER FOR FILE OPERATIONS. Use Sequential thinking MCP SERVER


https://docs.astro.build/en/reference/experimental-flags/preserve-scripts-order/

https://docs.astro.build/en/reference/directives-reference/#script--style-directives

https://docs.astro.build/en/guides/client-side-scripts


context files:

src/pages/booking.astro
