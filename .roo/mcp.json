{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/dev/radtransport"], "env": {}, "alwaysAllow": ["read_file", "read_multiple_files", "write_file", "edit_file", "create_directory", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories"]}, "firecrawl": {"type": "stdio", "command": "env", "args": ["FIRECRAWL_API_KEY=fc-ac38eecf6bc046529ba13666bc8e3809", "npx", "-y", "firecrawl-mcp"], "env": {}, "alwaysAllow": ["firecrawl_scrape", "firecrawl_map", "firecrawl_crawl", "firecrawl_batch_scrape", "firecrawl_check_batch_status", "firecrawl_check_crawl_status", "firecrawl_search", "firecrawl_extract", "firecrawl_deep_research", "firecrawl_generate_llmstxt"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "alwaysAllow": ["fetch"]}}}