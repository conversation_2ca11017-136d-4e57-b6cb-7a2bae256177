import eslintPluginAstro from 'eslint-plugin-astro';
import js from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
  // Base JavaScript recommended rules
  js.configs.recommended,
  
  // TypeScript recommended rules
  ...tseslint.configs.recommended,
  
  // Astro recommended rules and A11Y rules
  ...eslintPluginAstro.configs.recommended,
  ...eslintPluginAstro.configs['jsx-a11y-recommended'],
  
  // Global configuration
  {
    languageOptions: {
      globals: {
        // Global variables for Astro
        astroHTML: 'readonly',
      },
    },
    rules: {
      // TypeScript specific overrides
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/no-explicit-any': 'warn',
      
      // Astro specific rules
      'astro/missing-client-only-directive-value': 'error',
      'astro/no-conflict-set-directives': 'error',
      'astro/no-deprecated-astro-canonicalurl': 'error',
      'astro/no-deprecated-astro-fetchcontent': 'error',
      'astro/no-deprecated-astro-resolve': 'error',
      'astro/no-deprecated-getentrybyslug': 'error',
      'astro/no-unused-define-vars-in-style': 'error',
      'astro/valid-compile': 'error',
      'astro/no-set-html-directive': 'error',
      'astro/no-set-text-directive': 'error',
      'astro/no-unused-css-selector': 'warn', // Changed to warn as it can be overly strict
      'astro/prefer-class-list-directive': 'error',
      'astro/prefer-object-class-list': 'error',
      'astro/prefer-split-class-list': 'error',
      'astro/semi': 'error',
    },
  },
  
  // Specific configuration for Astro files
  {
    files: ['**/*.astro'],
    languageOptions: {
      parser: 'astro-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser',
        extraFileExtensions: ['.astro'],
        sourceType: 'module',
      },
    },
    env: {
      node: true,
      'astro/astro': true,
      es2020: true,
    },
  },
  
  // Configuration for script tags in Astro files (JavaScript)
  {
    files: ['**/*.astro/*.js', '*.astro/*.js'],
    languageOptions: {
      parserOptions: {
        sourceType: 'module',
      },
    },
    env: {
      browser: true,
      es2020: true,
    },
    rules: {
      // Disable prettier in script tags as they're formatted as .astro files
      'prettier/prettier': 'off',
    },
  },
  
  // Configuration for script tags in Astro files (TypeScript)
  {
    files: ['**/*.astro/*.ts', '*.astro/*.ts'],
    languageOptions: {
      parser: '@typescript-eslint/parser',
      parserOptions: {
        sourceType: 'module',
        project: null,
      },
    },
    env: {
      browser: true,
      es2020: true,
    },
    rules: {
      // Disable prettier in script tags as they're formatted as .astro files
      'prettier/prettier': 'off',
    },
  },
];
