import eslintPluginAstro from 'eslint-plugin-astro';
import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import typescriptParser from '@typescript-eslint/parser';
import astroParser from 'astro-eslint-parser';

export default [
  // Base JavaScript recommended rules
  js.configs.recommended,

  // TypeScript recommended rules
  ...tseslint.configs.recommended,

  // Astro recommended rules and A11Y rules
  ...eslintPluginAstro.configs.recommended,
  ...eslintPluginAstro.configs['jsx-a11y-recommended'],

  // Global ignores - exclude generated files and build artifacts
  {
    ignores: [
      'dist/**/*',
      '.astro/**/*',
      'node_modules/**/*',
      '**/*.min.js',
      '**/*.bundle.js',
    ],
  },

  // Global configuration
  {
    rules: {
      // TypeScript specific overrides
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/no-explicit-any': 'warn',

      // Additional Astro specific rules (beyond what's in recommended)
      'astro/missing-client-only-directive-value': 'error',
      'astro/no-deprecated-astro-canonicalurl': 'error',
      'astro/no-deprecated-astro-fetchcontent': 'error',
      'astro/no-deprecated-astro-resolve': 'error',
      'astro/no-deprecated-getentrybyslug': 'error',
      'astro/no-set-html-directive': 'warn', // Changed to warn as it's often needed for content
      'astro/no-set-text-directive': 'error',
      'astro/no-unused-css-selector': 'warn',
      'astro/prefer-class-list-directive': 'error',
      'astro/prefer-object-class-list': 'error',
      'astro/prefer-split-class-list': 'warn', // Temporarily set to warn due to parser issues with complex templates
      'astro/semi': 'error',
    },
  },

  // Configuration for Node.js files (config files, etc.)
  {
    files: ['**/*.config.{js,mjs,cjs}', '**/*.mjs', '**/tasks.mjs'],
    languageOptions: {
      globals: {
        // Node.js globals
        global: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        module: 'readonly',
        require: 'readonly',
        exports: 'readonly',
        console: 'readonly',
        URL: 'readonly',
      },
    },
    rules: {
      '@typescript-eslint/no-require-imports': 'off',
      'no-undef': 'off', // Node.js files often use globals
    },
  },

  // Specific configuration for Astro files
  {
    files: ['**/*.astro'],
    languageOptions: {
      parser: astroParser,
      parserOptions: {
        parser: typescriptParser,
        extraFileExtensions: ['.astro'],
        sourceType: 'module',
      },
      globals: {
        // Astro global variables
        astroHTML: 'readonly',
        // Node.js globals
        global: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        // ES2020 globals are included by default in modern ESLint
      },
    },
  },

  // Configuration for script tags in Astro files (JavaScript)
  {
    files: ['**/*.astro/*.js', '*.astro/*.js'],
    languageOptions: {
      parserOptions: {
        sourceType: 'module',
      },
      globals: {
        // Browser globals
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        console: 'readonly',
        // ES2020 globals are included by default
      },
    },
    rules: {
      // Disable prettier in script tags as they're formatted as .astro files
      'prettier/prettier': 'off',
    },
  },

  // Configuration for script tags in Astro files (TypeScript)
  {
    files: ['**/*.astro/*.ts', '*.astro/*.ts'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        sourceType: 'module',
        project: null,
      },
      globals: {
        // Browser globals
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        console: 'readonly',
        // ES2020 globals are included by default
      },
    },
    rules: {
      // Disable prettier in script tags as they're formatted as .astro files
      'prettier/prettier': 'off',
    },
  },
];
