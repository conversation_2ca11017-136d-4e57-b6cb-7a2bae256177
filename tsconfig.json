{"extends": "astro/tsconfigs/strict", "include": [".astro/types.d.ts", "**/*"], "exclude": ["dist"], "compilerOptions": {"plugins": [{"name": "@astrojs/ts-plugin"}], "allowImportingTsExtensions": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"~/*": ["src/*"], "@components/*": ["src/components/*"], "@layouts/*": ["src/layouts/*"], "@pages/*": ["src/pages/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"], "@types/*": ["src/types/*"]}}}