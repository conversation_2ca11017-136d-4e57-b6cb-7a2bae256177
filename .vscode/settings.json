{
  "css.customData": ["./vscode.tailwind.json"],
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "astro", // Enable .astro
    "typescript", // Enable .ts
    "typescriptreact" // Enable .tsx
  ],
  "files.associations": {
    "*.mdx": "markdown"
  },
  "prettier.documentSelectors": ["**/*.astro"],
  "[astro]": {
    "editor.defaultFormatter": "astro-build.astro-vscode"
  },
  "yaml.schemas": {
    "./.vscode/astrowind/config-schema.json": "/src/config.yaml"
  },
  "typescript.tsdk": "node_modules/typescript/lib"
}
