{"name": "radtransport", "description": "Rad Transport", "version": "1.0.0-beta.12", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "format": "prettier -w \"**/*.{js,ts,astro}\"", "lint:eslint": "eslint .", "lint": "bunx --bun eslint"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/rss": "^4.0.11", "@astrojs/sitemap": "^3.4.0", "@astrojs/ts-plugin": "^1.10.4", "@astrolib/analytics": "^0.6.1", "@astrolib/seo": "^1.0.0-beta.8", "@eslint/js": "^9.27.0", "@fontsource-variable/inter": "^5.2.5", "@fontsource-variable/raleway": "^5.2.5", "@types/js-yaml": "^4.0.9", "@unpic/astro": "^0.1.0", "@vercel/speed-insights": "^1.2.0", "astro": "^5.8.0", "astro-icon": "^1.1.5", "caniuse-lite": "^1.0.30001718", "iframe-resizer": "5.3.2", "limax": "4.1.0", "lodash.merge": "^4.6.2", "sharp": "^0.33.5", "tailwindcss-animated": "^1.1.2", "typescript-esbuild": "^0.4.10", "typescript-eslint": "^8.32.1", "unpic": "^3.22.0"}, "devDependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/partytown": "^2.1.4", "@astrojs/tailwind": "^5.1.5", "@iconify-json/flat-color-icons": "^1.2.1", "@iconify-json/tabler": "^1.2.18", "@tailwindcss/typography": "^0.5.16", "@types/lodash.merge": "^4.6.9", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "js-yaml": "^4.1.0", "mdast-util-to-string": "^4.0.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "reading-time": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-intersect": "^2.2.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20.0.0"}, "trustedDependencies": ["@parcel/watcher", "@vercel/speed-insights", "esbuild", "sharp"]}