/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />

declare module 'tailwindcss-intersect' {
  export const Observer: {
    start(): void;
  };
}

// Extend window and globalThis with custom properties
declare global {
  var dataLayer: any[];
  var basic_script: boolean;
  var iframeResizer: (options: object, selector: string) => void;
}

// Window interface extension (client-side only)
interface Window {
  dataLayer: any[];
  basic_script: boolean;
  iframeResizer: (options: object, selector: string) => void;
}

// Extend JSX to allow custom attributes for external scripts
declare namespace astroHTML.JSX {
  interface ScriptHTMLAttributes {
    config?: string;
  }
}
