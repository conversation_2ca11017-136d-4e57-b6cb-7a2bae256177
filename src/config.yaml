site:
  name: Rad Transport
  site: 'https://radtransport.com'
  base: '/'
  trailingSlash: false

  googleSiteVerificationId: orcPxI47GSa-cRvY11tUe6iGg2IO_RPvnA1q95iEM3M

# Default SEO metadata
metadata:
  title:
    default: Rad Transport
    template: '%s — Rad Transport'
  description: Rain or Snow, We Get You Where Ya Wanna Go
  robots:
    index: true
    follow: true
  openGraph:
    site_name: Rad Transport
    images:
      - url: '~/assets/images/default.png'
        width: 1200
        height: 628
    type: website
  twitter:
    handle: '@radtransport'
    site: '@radtransport'
    cardType: summary_large_image

i18n:
  language: en
  textDirection: ltr

apps:
  blog:
    isEnabled: true
    postsPerPage: 6

    post:
      isEnabled: true
      permalink: '/%slug%' # Variables: %slug%, %year%, %month%, %day%, %hour%, %minute%, %second%, %category%
      robots:
        index: true

    list:
      isEnabled: true
      pathname: 'blog' # Blog main path, you can change this to "articles" (/articles)
      robots:
        index: true

    category:
      isEnabled: true
      pathname: 'category' # Category main path /category/some-category, you can change this to "group" (/group/some-category)
      robots:
        index: true

    tag:
      isEnabled: true
      pathname: 'tag' # Tag main path /tag/some-tag, you can change this to "topics" (/topics/some-category)
      robots:
        index: false

analytics:
  vendors:
    googleTagManager:
      id: 'GTM-PKQKWTQB' 
      partytown: true
    googleAnalytics:
      id: 'UA-XXXXXX-X'

ui:
  theme: 'dark:only' # Values: "system" | "light" | "dark" | "light:only" | "dark:only"

  tokens:
    default:
      fonts:
        sans: 'Raleway Variable'
        serif: var(--ph-font-sans)
        heading: var(--ph-font-sans)
      colors:
        default: rgb(16 16 16)
        heading: rgb(0 0 0)
        muted: rgb(40 40 40)
        bgPage: rgb(255 255 255)
        primary: rgb(0 124 220)
        #primary: rgb(191 219 254)#
        secondary: rgb(30 58 138)
        accent: rgb(109 40 217)
        info: rgb(119 182 234)
        success: rgb(54 211 153)
        warning: rgb(251 189 35)
        error: rgb(248 114 114)
        link: var(--ph-color-primary)
        linkActive: var(--ph-color-link)
    dark:
      fonts: {}
      colors:
        default: rgb(247, 248, 248)
        heading: rgb(247, 248, 248)
        muted: rgb(200, 188, 208)
        bgPage: rgb(0, 0, 0)
        #primary: rgb(29 78 216)#
        primary: rgb(101 190 230)
        secondary: rgb(30 58 138)
        accent: rgb(135 77 2267)
        info: rgb(58 191 248)
        success: rgb(54 211 153)
        warning: rgb(251 189 35)
        error: rgb(248 114 114)
        link: var(--ph-color-primary)
        linkActive: var(--ph-color-link)
