---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';
/* import Announcement from '~/components/widgets/Announcement.astro'; */

import { headerData, footerData } from '~/navigation';

import type { MetaData } from '~/types';

export interface Props {
  metadata?: MetaData;
}

const { metadata } = Astro.props;
---

<Layout metadata={metadata}>
  <!--   <slot name="announcement">
    <Announcement />
  </slot> -->
  <slot name="header">
    <Header
      {...headerData as Props}
      isSticky
    />
  </slot>
  <slot name="hero" /> {/* New slot for full-width hero */}
  <main
    class="overflow-x-hidden"
    transition:animate="fade"
  >
    <slot /> {/* Default slot for main content (will be containerized) */}
  </main>
  <slot name="footer">
    <Footer {...footerData} />
  </slot>
</Layout>
