---
import Layout from '~/layouts/PageLayout.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Content from '~/components/widgets/Content.astro';

const metadata = {
  title: 'Book A Trip',
};
---

<script
  is:inline
  src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.9/iframeResizer.min.js"
  integrity="sha512-+bpyZqiNr/4QlUd6YnrAeLXzgooA1HKN5yUagHgPSMACPZgj8bkpCyZezPtDy5XbviRm4w8Z1RhfuWyoWaeCyg=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<script data-astro-rerun is:inline>
  iFrameResize(
    {
      log: true, // Enable console logging for debugging purposes

     

      onMessage: function (messageData) {
        console.log('Received message from iframe:', messageData.message); // Diagnostic log

        try {
          // Assuming messageData.message is a stringified JSON

          var msg = JSON.parse(messageData.message);

          console.log('Pushing event to dataLayer:', msg);

          window.dataLayer = window.dataLayer || [];

          window.dataLayer.push(msg);
        } catch (e) {
          console.error('Error processing message from iframe:', e);
        }
      },
    },

    '#ores'
  ); // Ensure the selector matches your iframe's ID or class
</script>
<Layout metadata={metadata}>
  <Content class="my-0 py-0">
    <Fragment slot="title">
      <p class="text-title-small md:text-title">Reservations</p>
    </Fragment>
  </Content>
  <WidgetWrapper containerClass={`max-w-7xl mx-auto mt-4 px-4 `}>
    <iframe
      title="ores"
      id="ores"
      src="https://book.mylimobiz.com/v4/radtransport"
      style="width:1px; min-width:100%"
      loading="lazy"
    ></iframe>
  </WidgetWrapper>
</Layout>
