---
import Layout from '~/layouts/PageLayout.astro';
import { ourayMountainDayTour as tour } from '~/data/tourData.js';
import Image from '~/components/common/Image.astro';
import Button from '~/components/ui/Button.astro';
import Headline from '~/components/ui/Headline.astro';
import Icon from '~/components/common/IconWrapper.astro';

const metadata = {
  title: tour.meta.title,
  description: tour.meta.description,
  canonical: Astro.url.href,
};

// Prepare image data for client-side script
const clientFriendlyGalleryImages = tour.imageGallery.images.map(img => ({
  src: img.src.src, // Extract the string path
  alt: img.alt,
  caption: img.caption,
}));
---

<Layout metadata={metadata}>


  <section slot="hero" class="relative w-screen h-[65vh] md:h-[75vh] flex items-center justify-center text-center text-white">
    <Image
      src={tour.hero.backgroundImage}
      alt="Panoramic view of San Juan Mountains"
      class="absolute inset-0 w-full h-full object-cover object-top -z-10"
      widths={[400, 768, 1024, 2048, 2560]}
      sizes="(max-width: 767px) 100vw, 100vw"
      width={2560}
      height={1396}
      loading="eager"
      decoding="async"
    />
    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/10 -z-10"></div>
    <div class="relative z-10 p-4">
      <Headline tag="h1" class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight text-shadow-lg">
        {tour.hero.title}
      </Headline>
      <p class="text-lg md:text-xl lg:text-2xl mb-8 max-w-3xl mx-auto text-shadow-lg">{tour.hero.tagline}</p>
      <Button variant="primary" size="xl" href={tour.hero.ctaBook.href} target="_blank" rel="noopener noreferrer" class="transform hover:scale-105 transition-transform duration-200">
        {tour.hero.ctaBook.text}
      </Button>
    </div>
  </section>

  {/* Main page content - default slot */}
  <div class="bg-black text-white py-12 md:py-16">
    <div class="container mx-auto px-4 space-y-16 md:space-y-24">

      <!-- Combined Key Details & Overview Section -->
      <section>
        <div class="max-w-6xl mx-auto grid md:grid-cols-5 gap-8 md:gap-12 items-start">
          <div class="md:col-span-3">
            <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-4 text-primary">
              {tour.overview.title}
            </Headline>
            <p class="text-lg text-slate-300 leading-relaxed">
              {tour.overview.text}
            </p>
          </div>
          <div class="md:col-span-2 bg-slate-800/80 backdrop-blur-[10px] border border-white/10 p-6 rounded-xl shadow-2xl sticky top-24">
            <Headline tag="h3" class="text-2xl font-semibold mb-6 text-center text-primary">
              {tour.keyDetails.title}
            </Headline>
            <ul class="space-y-4">
              {tour.keyDetails.items.map((item: any) => (
                <li class="flex items-start text-md group hover:bg-slate-700/50 p-3 rounded-lg transition-all duration-300">
                  {item.icon && (
                    <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5 group-hover:bg-primary/30 transition-colors">
                      <Icon name={item.icon} class="w-5 h-5 text-primary" />
                    </div>
                  )}
                  <div>
                    <span class="font-semibold text-slate-200 block">{item.label}</span>
                    <p class="text-slate-300 text-sm">{item.value}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      <!-- Detailed Itinerary Section - Vertical Timeline -->
      <section>
        <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-10 text-center text-primary">
          {tour.itinerary.title}
        </Headline>
        <div class="relative max-w-3xl mx-auto">
          <div class="absolute left-1/2 top-0 bottom-0 w-1 bg-slate-700 transform -translate-x-1/2 hidden md:block"></div>
          {tour.itinerary.timeline.map((item: any, index: number) => {
            const iconContainerClasses = ["hidden", "md:flex", "flex-col", "items-center", "mr-8"];
            if (index % 2 === 0) {
              iconContainerClasses.push("md:order-1");
            } else {
              iconContainerClasses.push("md:order-3");
            }

            const contentBoxClasses = ["bg-slate-800", "p-6", "rounded-lg", "shadow-xl", "w-full", "md:w-[calc(50%-4rem)]", "transition-all", "duration-300", "ease-in-out", "hover:-translate-y-1", "hover:shadow-[0_10px_30px_rgba(0,0,0,0.3)]"];
            if (index % 2 === 0) {
              contentBoxClasses.push("md:order-2", "md:ml-auto");
            } else {
              contentBoxClasses.push("md:order-2", "md:mr-auto");
            }

            return (
            <div class="timeline-item mb-8 md:mb-12 flex md:items-center w-full transition-all duration-700 ease-out opacity-0 translate-y-[20px]">
              <div class:list={iconContainerClasses}>
                <div class="w-12 h-12 rounded-full flex items-center justify-center text-white mb-1 shadow-md bg-primary transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-[0_8px_25px_rgba(30,64,175,0.3)]">
                  {item.icon && <Icon name={item.icon} class="w-6 h-6" />}
                </div>
                <div class="font-bold text-primary text-sm">{item.time}</div>
              </div>
              <div class:list={contentBoxClasses}>
                <div class="flex items-center mb-3 md:hidden">
                   <div class="w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 shadow-md bg-primary transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-[0_8px_25px_rgba(30,64,175,0.3)]">
                    {item.icon && <Icon name={item.icon} class="w-5 h-5" />}
                  </div>
                  <div class="font-bold text-primary text-md">{item.time}</div>
                </div>
                <h3 class="text-xl font-semibold mb-2 text-slate-100">{item.activity}</h3>
                {item.description && <p class="text-slate-300 text-sm leading-relaxed">{item.description}</p>}
              </div>
            </div>
            );
          })}
        </div>
      </section>

      <!-- What's Included Section -->
      <section>
        <div class="max-w-3xl mx-auto">
          <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-8 text-center text-primary">
            {tour.whatsIncluded.title}
          </Headline>
          <div class="bg-slate-800/80 backdrop-filter backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-2xl border border-slate-700/50">
            <ul class="space-y-4 text-lg text-slate-300 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-4">
              {tour.whatsIncluded.items.map((item: any) => (
                <li class="flex items-center group hover:bg-slate-700/30 p-3 rounded-lg transition-all duration-300">
                  {item.icon && (
                    <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover:bg-primary/30 transition-colors">
                      <Icon name={item.icon} class="w-5 h-5 text-primary" />
                    </div>
                  )}
                  <span class="leading-relaxed">{item.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      <!-- Image Gallery with Native Dialog Lightbox -->
      <section>
        <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-8 text-center text-primary">
          {tour.imageGallery.title}
        </Headline>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {tour.imageGallery.images.map((image, index) => (
            <button
              type="button"
              data-lightbox-trigger
              data-image={image.src}
              data-alt={image.alt}
              data-caption={image.caption}
              data-index={index}
              class="group block rounded-xl overflow-hidden shadow-lg relative transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-[0_15px_35px_rgba(0,0,0,0.4)] before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br before:from-blue-600/10 before:to-purple-600/10 before:opacity-0 before:transition-opacity before:duration-300 before:z-[1] hover:before:opacity-100 cursor-pointer w-full border-0 p-0 bg-transparent"
            >
              <Image
                src={image.src}
                alt={image.alt}
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                widths={[300, 600, 900]}
                sizes="(max-width: 639px) 100vw, (max-width: 1023px) 50vw, 33vw"
                aspectRatio="4:3"
                width={400}
                loading="lazy"
                decoding="async"
              />
              {image.caption && (
                <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/50 to-transparent transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 z-10">
                  <p class="text-white text-sm font-semibold text-shadow-lg">{image.caption}</p>
                </div>
              )}
              <div class="absolute top-3 right-3 w-8 h-8 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                <Icon name="tabler:zoom-in" class="w-4 h-4 text-white" />
              </div>
            </button>
          ))}
        </div>
      </section>

      <!-- Call to Action (Bottom of Page) -->
      <section class="text-center pt-10 pb-4">
         <div class="max-w-md mx-auto bg-slate-800 p-6 md:p-8 rounded-lg shadow-xl">
          <Headline tag="h3" class="text-2xl font-semibold mb-6 text-primary">Ready for an Unforgettable Adventure?</Headline>
          <div class="space-y-4">
            <Button variant="primary" size="xl" href={tour.ctaBottom.bookNow.href} target="_blank" rel="noopener noreferrer" class="w-full transform hover:scale-105 transition-transform duration-200">
              {tour.ctaBottom.bookNow.text}
            </Button>
            <Button variant="secondary" size="lg" href={tour.ctaBottom.callForQuote.href} class="w-full">
              {tour.ctaBottom.callForQuote.text}
            </Button>
          </div>
        </div>
      </section>
    </div>
  </div>

  <!-- Native Dialog Lightbox -->
  <dialog id="lightbox-dialog" class="backdrop:bg-black/90 backdrop:backdrop-blur-sm max-w-none max-h-none w-full h-full p-0 border-0 bg-transparent">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="relative max-w-[95vw] max-h-[95vh] bg-black/95 rounded-lg overflow-hidden shadow-2xl">
        <!-- Close button -->
        <button
          type="button"
          id="lightbox-close"
          class="absolute top-4 right-4 z-50 w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
          aria-label="Close lightbox"
        >
          <Icon name="tabler:x" class="w-6 h-6" />
        </button>
        
        <!-- Navigation buttons -->
        <button
          type="button"
          id="lightbox-prev"
          class="absolute left-4 top-1/2 -translate-y-1/2 z-50 w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Previous image"
        >
          <Icon name="tabler:chevron-left" class="w-6 h-6" />
        </button>
        
        <button
          type="button"
          id="lightbox-next"
          class="absolute right-4 top-1/2 -translate-y-1/2 z-50 w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Next image"
        >
          <Icon name="tabler:chevron-right" class="w-6 h-6" />
        </button>
        
        <!-- Image container -->
        <div class="relative">
          <img
            id="lightbox-image"
            src=""
            alt=""
            class="max-w-full max-h-[90vh] object-contain w-auto h-auto"
            loading="lazy"
          />
          
          <!-- Caption -->
          <div id="lightbox-caption" class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 to-transparent text-white text-center opacity-0 transition-opacity duration-300">
            <p class="text-sm"></p>
          </div>
        </div>
        
        <!-- Image counter -->
        <div class="absolute top-4 left-4 z-50 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
          <span id="lightbox-counter">1 / 1</span>
        </div>
      </div>
    </div>
  </dialog>

  <!-- Floating CTA Button -->
  <div id="floating-cta" class="fixed bottom-[10px] right-[10px] left-[10px] md:bottom-5 md:right-5 md:left-auto z-50 transition-all duration-300 ease-out opacity-0 translate-y-[100px]">
    <div class="bg-primary text-white px-4 py-2 rounded-full shadow-2xl flex items-center space-x-2 cursor-pointer hover:bg-primary/90 transition-colors">
      <Icon name="tabler:calendar" class="w-5 h-5" />
      <span class="hidden sm:inline font-semibold">Book Now</span>
      <span class="sm:hidden font-semibold">Book</span>
    </div>
  </div>

  <script define:vars={{ galleryImages: clientFriendlyGalleryImages }} is:inline>
    // Lightbox functionality
    let currentImageIndex = 0;
    const dialog = document.getElementById('lightbox-dialog');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCaption = document.getElementById('lightbox-caption');
    const lightboxCounter = document.getElementById('lightbox-counter');
    const closeButton = document.getElementById('lightbox-close');
    const prevButton = document.getElementById('lightbox-prev');
    const nextButton = document.getElementById('lightbox-next');

    function openLightbox(index) {
      currentImageIndex = index;
      updateLightboxContent();
      dialog.showModal();
      document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
      dialog.close();
      document.body.style.overflow = '';
    }

    function updateLightboxContent() {
      const image = galleryImages[currentImageIndex];
      lightboxImage.src = image.src; // Now image.src is the direct string path
      lightboxImage.alt = image.alt;
lightboxImage.style.opacity = '1';
      
      // Update caption
      const captionElement = lightboxCaption.querySelector('p');
      if (image.caption) {
        captionElement.textContent = image.caption;
        lightboxCaption.style.opacity = '1';
      } else {
        lightboxCaption.style.opacity = '0';
      }
      
      // Update counter
      lightboxCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
      
      // Update navigation buttons
      if (galleryImages.length > 1) {
        prevButton.disabled = false;
        nextButton.disabled = false;
      } else {
        prevButton.disabled = true;
        nextButton.disabled = true;
      }
    }

    function showPrevImage() {
      currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
      updateLightboxContent();
    }

    function showNextImage() {
      currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
      updateLightboxContent();
    }

    // Event listeners
    document.querySelectorAll('[data-lightbox-trigger]').forEach((trigger, index) => {
      trigger.addEventListener('click', () => openLightbox(index));
    });

    closeButton.addEventListener('click', closeLightbox);
    prevButton.addEventListener('click', showPrevImage);
    nextButton.addEventListener('click', showNextImage);

    // Close on backdrop click
    dialog.addEventListener('click', (e) => {
      if (e.target === dialog) {
        closeLightbox();
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (!dialog.open) return;
      
      switch (e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          showPrevImage();
          break;
        case 'ArrowRight':
          showNextImage();
          break;
      }
    });

    // Floating CTA functionality
    const floatingCta = document.getElementById('floating-cta');
    const heroSection = document.querySelector('[slot="hero"]');
    const ctaSection = document.querySelector('section:last-of-type');
    
    function toggleFloatingCta() {
      const heroRect = heroSection?.getBoundingClientRect();
      const ctaRect = ctaSection?.getBoundingClientRect();
      
      if (floatingCta && heroRect && ctaRect) {
        const heroVisible = heroRect.bottom > 0;
        const ctaVisible = ctaRect.top < window.innerHeight;
        
        if (!heroVisible && !ctaVisible) {
          floatingCta.classList.remove('opacity-0', 'translate-y-[100px]');
          floatingCta.classList.add('opacity-100', 'translate-y-0');
        } else {
          floatingCta.classList.remove('opacity-100', 'translate-y-0');
          floatingCta.classList.add('opacity-0', 'translate-y-[100px]');
        }
      }
    }
    
    window.addEventListener('scroll', toggleFloatingCta);
    
    floatingCta?.addEventListener('click', () => {
      window.open('https://book.mylimobiz.com/v4/radtransport', '_blank');
    });
    
    // Intersection Observer for timeline animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const element = entry.target;
          element.classList.remove('opacity-0', 'translate-y-[20px]');
          element.classList.add('opacity-100', 'translate-y-0');
          obs.unobserve(element); // Stop observing after animation
        }
      });
    }, observerOptions);
    
    document.querySelectorAll('.timeline-item').forEach((item) => {
      observer.observe(item);
    });
    
    // Smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const hrefAttribute = this.getAttribute('href');
        
        if (hrefAttribute && hrefAttribute.trim() !== '' && hrefAttribute !== '#') {
          try {
            const targetElement = document.querySelector(hrefAttribute);
            if (targetElement) {
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            } else {
              console.warn(`Smooth scroll: Target element not found for selector '${hrefAttribute}'`);
            }
          } catch (error) {
            console.error(`Smooth scroll: Invalid selector '${hrefAttribute}'. Details:`, error);
          }
        } else if (hrefAttribute === '#') {
          console.warn('Smooth scroll: Link with href="#" was clicked. No specific target to scroll to, or default browser behavior prevented.');
        } else {
          console.warn('Smooth scroll: Anchor has no valid href attribute starting with #.');
        }
      });
    });
  </script>
</Layout>