---
import { ClientRouter } from 'astro:transitions';
import PageLayout from '~/layouts/PageLayout.astro';

import Features from '~/components/widgets/Features.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Testimonials from '~/components/widgets/Testimonials.astro';
import Button from '~/components/ui/Button.astro';
import { Icon } from 'astro-icon/components';

import gmcImage from 'src/assets/fleet/may-21-25-update/GMC Yukon XL .jpeg';
import mercedesImage from 'src/assets/fleet/may-21-25-update/Mercedes Benz LA West Image.jpg';
import { getPermalink } from '~/utils/permalinks';

const metadata = {
  title: 'Airport Transportation | Durango, Telluride, Montrose & Cortez',
  description: 'Travel in comfort with Rad Transport. Reliable airport transportation to Durango, Telluride, Montrose & Cortez. Book your ride today.',
  canonical: getPermalink('/airport-transportation'),
  keywords: 'airport transportation, airport shuttle Colorado, Durango airport transfer, Telluride transportation, Montrose limo service',
  ignoreTitleTemplate: false,
};
---

<PageLayout metadata={metadata}>
  <ClientRouter />
  
  <!-- Full Viewport Hero Section with Header Overlay -->
  <section class="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 min-h-dvh flex flex-col justify-start overflow-hidden pt-[6.5rem] sm:pt-32 md:pt-36 xl:pt-40 pb-6 lg:pb-12 xl:pb-16">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px); background-size: 40px 40px;"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 @container flex-grow flex flex-col justify-center">
      <div class="text-center">
        <!-- Responsive Badge -->
        <div class="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-blue-400/30 bg-blue-400/20 mb-4 sm:mb-8 mt-3 sm:mt-6 md:mt-8">
          <Icon name="tabler:map-pin" class="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2 text-blue-400" />
          <span class="text-xs sm:text-sm font-medium text-blue-400">Colorado's Premier Airport Service</span>
        </div>
        
        <!-- Modern Fluid Typography Heading -->
        <h1 class="text-[clamp(1.875rem,4vw,3rem)] sm:text-[clamp(2.25rem,4.5vw,3.5rem)] lg:text-[clamp(2.75rem,5vw,4rem)] font-bold text-white mb-3 sm:mb-6 leading-[1.1] sm:leading-tight">
          Reliable Airport Transportation
          <span class="block text-blue-400 mt-1 sm:mt-2 max-w-md sm:max-w-lg md:max-w-xl lg:max-w-3xl mx-auto">to Durango, Telluride, Montrose & Cortez</span>
        </h1>
        
        <!-- Responsive Description -->
        <p class="text-sm sm:text-base lg:text-lg text-gray-300 mb-4 sm:mb-8 max-w-2xl lg:max-w-4xl mx-auto leading-relaxed px-2 sm:px-0">
          Travel to and from Colorado's top mountain destinations with ease. Rad Transport offers premium airport transportation to Durango, Telluride, Montrose, and Cortez. Whether you're headed for the slopes, trails, or a business meeting, we ensure you arrive on time, in comfort, and stress-free.
        </p>
        
        <!-- Responsive CTA Buttons -->
        <div id="hero-buttons" class="flex flex-row gap-2 sm:gap-3 md:gap-4 justify-center items-center mb-3 sm:mb-6 lg:mb-8 px-1 sm:px-2 md:px-0">
          <Button
            variant="primary"
            text="Get Quote"
            href="#booking_form_widget"
            class="text-sm px-3 py-2 sm:text-base sm:px-4 sm:py-3 responsive-booking-link"
          />
          <Button
            variant="secondary"
            text="Call Now"
            href="tel:+***********"
            class="text-sm px-3 py-2 sm:text-base sm:px-4 sm:py-3"
          />
        </div>
        
        <!-- Trust Indicators - Always 2x2 Grid -->
        <div class="grid grid-cols-2 gap-2 sm:gap-4 md:gap-6 text-center max-w-2xl mx-auto px-2 sm:px-4 md:px-0">
          <div class="text-white py-1 sm:py-2">
            <div class="text-xl sm:text-2xl md:text-3xl font-bold text-blue-400">24/7</div>
            <div class="text-xs sm:text-sm text-gray-300 mt-0.5 sm:mt-1">Available</div>
          </div>
          <div class="text-white py-1 sm:py-2">
            <div class="flex items-center justify-center mb-0.5 sm:mb-1">
              <span class="text-lg sm:text-xl md:text-2xl font-bold text-blue-400 mr-1">5</span>
              <Icon name="tabler:star-filled" class="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-blue-400" />
            </div>
            <div class="text-xs sm:text-sm text-gray-300">Rated Service</div>
          </div>
          <div class="text-white py-1 sm:py-2">
            <div class="text-xl sm:text-2xl md:text-3xl font-bold text-blue-400">100%</div>
            <div class="text-xs sm:text-sm text-gray-300 mt-0.5 sm:mt-1">Private Rides</div>
          </div>
          <div class="text-white py-1 sm:py-2">
            <div class="text-xl sm:text-2xl md:text-3xl font-bold text-blue-400">Local</div>
            <div class="text-xs sm:text-sm text-gray-300 mt-0.5 sm:mt-1">Drivers</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Why Choose Section - Custom Layout -->
  <section class="py-16 md:py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid lg:grid-cols-2 gap-16 items-center mb-12">
        <div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Why Choose Rad Transport for Airport Transfers?
          </h2>
          <p class="text-base md:text-lg text-gray-600 mb-6 leading-relaxed">
            Avoid the stress of rental cars or unreliable shuttles. Rad Transport specializes in door-to-door airport service for travelers heading to or from Colorado's scenic Southwest.
          </p>
          <div class="space-y-4 mb-8">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mt-1">
                <Icon name="tabler:check" class="w-3 h-3 text-white" />
              </div>
              <p class="ml-3 text-base text-gray-600">We know the terrain and monitor flights</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mt-1">
                <Icon name="tabler:check" class="w-3 h-3 text-white" />
              </div>
              <p class="ml-3 text-base text-gray-600">Safety priority in snowy, high-altitude conditions</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mt-1">
                <Icon name="tabler:check" class="w-3 h-3 text-white" />
              </div>
              <p class="ml-3 text-base text-gray-600">Personalized, private service with no hidden fees</p>
            </div>
          </div>
          <div>
            <Button
              variant="primary"
              text="Get Your Quote"
              href="#booking_form_widget"
            />
          </div>
        </div>
        <div class="relative order-first lg:order-last mb-16 lg:mb-0">
          <!-- Vehicle Carousel -->
          <!-- Modern CSS Scroll Snap Carousel -->
          <div class="relative overflow-hidden rounded-lg shadow-xl">
            <div class="flex w-full snap-x snap-mandatory overflow-x-auto scrollbar-hide scroll-smooth">
              <div class="w-full flex-shrink-0 snap-center snap-always">
                <img src={gmcImage.src} alt="Rad Transport GMC Yukon XL luxury SUV" class="w-full h-auto object-cover" />
              </div>
              <div class="w-full flex-shrink-0 snap-center snap-always">
                <img src={mercedesImage.src} alt="Rad Transport Mercedes Benz luxury vehicle" class="w-full h-auto object-cover" />
              </div>
            </div>
            
            <!-- Visual indicators (optional - scroll position is now natural) -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 pointer-events-none">
              <div class="w-2 h-2 rounded-full bg-white/80 shadow-sm"></div>
              <div class="w-2 h-2 rounded-full bg-white/40 shadow-sm"></div>
            </div>
          </div>
          
          <div class="absolute -bottom-2 -right-2 bg-blue-500 text-white px-4 py-3 rounded-lg shadow-xl border-2 border-white">
            <div class="text-lg font-bold leading-tight">On-Time</div>
            <div class="text-xs opacity-90">Guarantee</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Travel in Comfort Section - Cards Layout -->
  <section class="py-16 md:py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Travel in Comfort – Every Season, Every Reason
        </h2>
        <p class="text-lg text-gray-600 max-w-3xl mx-auto mb-12">
          From powder-season ski trips to summer hiking adventures or business travel, RAD Transport provides well-maintained SUVs and vans suited for mountain roads and unpredictable weather.
        </p>
      </div>
      
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="h-48 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
            <Icon name="tabler:luggage" class="w-16 h-16 text-white" />
          </div>
          <div class="p-6">
            <h3 class="text-xl md:text-2xl font-bold text-gray-900 mb-3">Spacious Vehicles</h3>
            <p class="text-base text-gray-600">Plenty of room for luggage, skis, bikes, hiking gear, and more. No need to leave anything behind.</p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="h-48 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
            <Icon name="tabler:snowflake" class="w-16 h-16 text-white" />
          </div>
          <div class="p-6">
            <h3 class="text-xl md:text-2xl font-bold text-gray-900 mb-3">All-Season Ready</h3>
            <p class="text-base text-gray-600">Equipped for mountain roads and unpredictable weather conditions year-round.</p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="h-48 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
            <Icon name="tabler:user-heart" class="w-16 h-16 text-white" />
          </div>
          <div class="p-6">
            <h3 class="text-xl md:text-2xl font-bold text-gray-900 mb-3">Tailored Service</h3>
            <p class="text-base text-gray-600">Individual, family, or group transportation customized to your specific needs.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Key Features Section -->
  <div class="py-16 md:py-20">
    <Features
      title="Safe, Reliable, and Always On Time"
      subtitle="Experience the Rad Transport difference with our premium airport transportation service."
      classes={{
        container: 'px-4 sm:px-6 lg:px-8'
      }}
      items={[
        {
          title: 'Local Drivers Who Know the Terrain',
          description: 'Our trained drivers are based in Colorado and specialize in navigating mountain roads, making your journey safer and more efficient—no matter the season.',
          icon: 'tabler:mountain',
        },
        {
          title: 'Private Rides, No Crowds',
          description: 'Unlike shared airport shuttles, we offer private transportation with dedicated pickups and flexible scheduling. Your time, your comfort, your route.',
          icon: 'tabler:car',
        },
        {
          title: 'Spacious Vehicles for Outdoor Gear & Group Travel',
          description: "We've got the room. Bring your skis, bikes, hiking gear, or bags. Our fleet is designed to handle the extras without sacrificing comfort.",
          icon: 'tabler:luggage',
        },
        {
          title: 'Flight Monitoring',
          description: 'We track your flight status and adjust pickup times accordingly, ensuring we are there when you land, regardless of delays.',
          icon: 'tabler:plane-arrival',
        },
        {
          title: 'All-Weather Reliability',
          description: 'Colorado weather can be unpredictable. Our vehicles are equipped for mountain conditions and our drivers are experienced in all weather.',
          icon: 'tabler:snowflake',
        },
        {
          title: 'Door-to-Door Service',
          description: 'Complete airport transportation from pickup at your location to drop-off at the terminal, or from baggage claim to your final destination.',
          icon: 'tabler:door',
        },
      ]}
    >
      <Fragment slot="bg">
        <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
      </Fragment>
    </Features>
  </div>

  <!-- Testimonials Section -->
  <div class="py-16 md:py-20">
    <Testimonials
    title="Hear from Our Customers"
    subtitle="Don't just take our word for it - see what our satisfied customers have to say about our airport transportation service."
    classes={{
      container: 'px-4 sm:px-6 lg:px-8'
    }}
    testimonials={[
      {
        testimonial: 'Rad Transport made our ski trip perfect from the start. Professional driver, clean vehicle, and they tracked our flight when it was delayed. Highly recommend!',
        name: 'Sarah Johnson',
        job: 'Ski Enthusiast',
        image: {
          src: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
          alt: 'Sarah Johnson'
        }
      },
      {
        testimonial: 'Best airport transfer service in Colorado! Spacious vehicle fit all our family luggage and ski equipment. Driver was knowledgeable about the area.',
        name: 'Mike Rodriguez',
        job: 'Family Traveler',
        image: {
          src: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
          alt: 'Mike Rodriguez'
        }
      },
      {
        testimonial: 'Reliable, punctual, and comfortable. The vehicle was perfect for our group of 6 with all our hiking gear. Will definitely book again!',
        name: 'Emily Chen',
        job: 'Adventure Traveler',
        image: {
          src: 'https://images.unsplash.com/photo-1494790108755-2616b612c2bb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
          alt: 'Emily Chen'
        }
      },
    ]}
  />
  </div>

  <!-- Final CTA Section - Using your CallToAction component -->
  <div class="py-16 md:py-20">
    <CallToAction
    actions={[
      {
        variant: 'primary',
        text: 'Request a Quote',
        href: '#booking_section',
      },
      {
        variant: 'secondary',
        text: 'Call (*************',
        href: 'tel:+***********',
      },
    ]}
  >
    <Fragment slot="title">Book Your Airport Transfer Today</Fragment>
    <Fragment slot="subtitle">
      Skip the hassle—book a ride you can count on. Request a free quote or call to schedule your airport transportation with Rad today.
    </Fragment>
    <Fragment slot="tagline">
      Professional • Reliable • Comfortable<br />
      Serving Durango, Telluride, Montrose, and Cortez airports
    </Fragment>
  </CallToAction>
  </div>

  <!-- Quote Request Form Section - 3rd Party Form -->
  <section id="booking_section" class="py-16 md:py-20 bg-white">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="grid lg:grid-cols-2 gap-12 items-start">
        <!-- Left Column - Form Info -->
        <div>
          <div class="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white p-8 rounded-lg">
            <h2 class="text-2xl md:text-3xl font-bold mb-4">
              Request Your Airport Transfer Quote
            </h2>
            <p class="text-gray-200 mb-6">
              Fill out the form and we'll get back to you with a personalized quote within minutes.
            </p>
            <div class="space-y-4">
              <div class="flex items-center">
                <Icon name="tabler:clock" class="w-5 h-5 mr-3 text-gray-300" />
                <span class="text-gray-200">Instant quote response</span>
              </div>
              <div class="flex items-center">
                <Icon name="tabler:currency-dollar-off" class="w-5 h-5 mr-3 text-gray-300" />
                <span class="text-gray-200">No hidden fees</span>
              </div>
              <div class="flex items-center">
                <Icon name="tabler:plane-arrival" class="w-5 h-5 mr-3 text-gray-300" />
                <span class="text-gray-200">Flight monitoring included</span>
              </div>
            </div>
            <div class="mt-8 p-4 rounded-lg bg-slate-800/50">
              <p class="text-sm font-medium mb-2 text-gray-300">Need immediate assistance?</p>
              <a href="tel:+***********" class="text-white font-bold text-lg hover:opacity-80 transition-opacity">
                (*************
              </a>
            </div>
          </div>
        </div>
        
        <!-- Right Column - 3rd Party Form Widget -->
        <div class="bg-gray-50 rounded-lg shadow-lg">
          <div id="booking_form_widget" class="max-h-[420px] pt-4">
            <!-- The 3rd party form will be injected here -->
          </div>
        </div>
      </div>
    </div>
  </section>

  <style>
    /* Hide scrollbars while maintaining scroll functionality */
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }


  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Modern Intersection Observer for sticky footer visibility
      const heroButtons = document.getElementById('hero-buttons');
      const stickyFooter = document.getElementById('sticky-footer');
      
      if (heroButtons && stickyFooter) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            // Update data attribute based on visibility
            stickyFooter.setAttribute('data-hero-visible', entry.isIntersecting ? 'true' : 'false');
          });
        }, {
          // Trigger when hero buttons are 10% visible/hidden
          threshold: 0.1,
          // Add some margin to trigger slightly before/after
          rootMargin: '-20px 0px'
        });
        
        observer.observe(heroButtons);
      }
    });
  </script>


  <!-- Sticky CTA for mobile - Using your Button component -->
  <div
    id="sticky-footer"
    data-hero-visible="true"
    class="fixed bottom-0 left-0 right-0 z-[1000] bg-gradient-to-r from-blue-800 to-blue-600 p-3 px-4 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] transition-transform duration-300 ease-out data-[hero-visible=true]:translate-y-full md:hidden"
  >
    <div class="flex items-center gap-2">
      <Button
        variant="secondary"
        text="Get Quote"
        href="#booking_form_widget"
        class="flex-1 text-center"
      />
      <Button
        variant="primary"
        text="Call Now"
        href="tel:+***********"
        class="flex-1 text-center"
      />
    </div>
  </div>

</PageLayout>