---
import Content from '~/components/widgets/Content.astro';
import FAQs from '~/components/widgets/FAQs.astro';
import Layout from '~/layouts/PageLayout.astro';
import { ClientRouter } from 'astro:transitions';
import { fade } from 'astro:transitions';
const metadata = {
  title: 'FAQ - Rad Transport',
  ignoreTitleTemplate: true,
};
---
<Layout metadata={metadata}>
  <ClientRouter />
  
  <Content 
    transition:animate={fade({ duration: '0.5s' })}
    id="faq-hero"
  >
    <Fragment slot="title">
      <div class="translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">
        <p class="text-[#65bee6] font-[Raleway] text-xl font-medium uppercase italic leading-[1em] tracking-[1.3px]">Frequently Asked Questions</p>
      </div>
    </Fragment>
    <Fragment slot="content">
      <div class="space-y-4">
        <h2 class="text-lg md:text-xl font-medium mb-8 -translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">
          Your Guide to Understanding Our 
          <span class="relative">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">Premium Transportation</span>
            <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
          </span> 
          Services
        </h2>
      </div>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <FAQs
    classes={{ 
      container: 'max-w-6xl mx-auto px-4 sm:px-6 lg:px-8',
      subtitle: 'text-base md:text-lg text-left text-muted mb-8 opacity-0 intersect:opacity-100 transition duration-1000 delay-200 intersect-once',
      title: 'text-left'
    }}
    items={[
      {
        title: "How do I book a ride with your company?",
        description:
          "Booking a ride with us is simple! You can book directly through our website, use our mobile app, or give us a call. We recommend booking at least 24 hours in advance to ensure vehicle availability, especially during peak seasons.",
      },
      {
        title: "What areas do you serve in and around Colorado?",
        description:
          "We provide transportation services throughout Colorado, with a special focus on the Durango area, including nearby towns like Telluride, Purgatory, and Silverton. Our services extend to all of Colorado, ensuring comprehensive coverage for your travel needs.",
      },
      {
        title: "What types of vehicles do you offer?",
        description:
          "Our fleet includes a variety of vehicles to suit your needs, from luxury sedans and SUVs for individual travelers and small groups to spacious vans and buses for larger parties. All vehicles are well-maintained, equipped with winter tires, and offer ample space for luggage and ski equipment.",
      },
      {
        title: "What is your cancellation policy?",
        description:
          "Cancellations made 24 hours before the scheduled pickup time are fully refundable. Cancellations made less than 24 hours prior to pickup are subject to a 50% cancellation fee. No-shows are charged the full fare.",
      },
      {
        title: "Do you offer transportation services for special events?",
        description:
          "Absolutely! We provide transportation solutions for a variety of special events, including weddings, corporate events, ski trips, and more. Contact us with your event details, and we will tailor a transportation plan to meet your needs.",
      },
      {
        title: "How do you ensure passenger safety, especially during winter conditions?",
        description:
          "Passenger safety is our top priority. Our drivers are highly experienced with winter driving conditions and receive specialized training. All vehicles are equipped with winter tires and undergo regular maintenance checks to ensure a safe and comfortable journey.",
      },
    ]}
  />
</Layout>
