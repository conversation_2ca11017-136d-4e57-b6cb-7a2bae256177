---
import Layout from '~/layouts/PageLayout.astro';

import Hero from '~/components/widgets/Hero.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

import Features2 from '~/components/widgets/Features2.astro';
import Features from '~/components/widgets/Features.astro';
import Stats from '~/components/widgets/Stats.astro';
import Features3 from '~/components/widgets/Features3.astro';
import FAQs from '~/components/widgets/FAQs.astro';
import Brands from '~/components/widgets/Brands.astro';

const metadata = {
  title: 'Startup Landing Page',
};
---

<Layout metadata={metadata}>
  <!-- Hero Widget ******************* -->

  <Hero
    tagline="Startup Web Demo"
    actions={[
      {
        variant: 'primary',
        target: '_blank',
        text: 'Get templates',
        href: 'https://github.com/onwidget/astrowind',
        icon: 'tabler:download',
      },
      { text: 'Learn more', href: '#features' },
    ]}
  >
    <Fragment slot="title">
      Improve <span class="hidden sm:inline">the online presence of</span> your <span
        class="text-accent dark:text-white highlight">Startup</span
      > with Astrowind templates
    </Fragment>

    <Fragment slot="subtitle">
      Step into the spotlight with <span class="font-semibold">Astrowind</span> templates,
      your pathway to fortifying your startup's digital footprint, fostering credibility, and
      expanding your reach.
    </Fragment>

    <Fragment slot="image">
      <div class="relative h-0 pb-[56.25%]">
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/dsTXcSeAZq8"
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture;"
          allowfullscreen
          class="absolute top-0 left-0 w-full h-full"
        >
        </iframe>
      </div>
    </Fragment>
  </Hero>

  <!-- Features2 Widget ************** -->

  <Features2
    title="About us"
    subtitle="We believe in the magic of turning dreams into stunning realities. Founded by passionate developers with a shared vision, we set out to simplify the website creation process. Our templates bring together the innovation of Astro 4.0 and the versatility of Tailwind CSS, enabling you to express your unique brand identity like never before."
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Features2>

  <!-- Stats Widget ****************** -->

  <Stats
    title="Discover the impressive impact of Astrowind"
    subtitle="The numbers below reflect the trust our users have placed in us and the remarkable outcomes we've helped them achieve."
    stats={[
      { title: 'Downloads', amount: '182K' },
      { title: 'Websites Launched', amount: '87' },
      { title: 'User Ratings', amount: '4.8' },
      { title: 'Satisfied Clients', amount: '116K' },
    ]}
  />

  <!-- Brands Widget ****************** -->

  <Brands
    title="Partnerships & Collaborations"
    subtitle="At Astrowind, we believe in the power of collaboration to drive innovation and create exceptional experiences."
    icons={[]}
    images={[
      {
        src: 'https://cdn.pixabay.com/photo/2015/05/26/09/37/paypal-784404_1280.png',
        alt: 'Paypal',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2021/12/06/13/48/visa-6850402_1280.png',
        alt: 'Visa',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2013/10/01/10/29/ebay-189064_1280.png',
        alt: 'Ebay',
      },

      {
        src: 'https://cdn.pixabay.com/photo/2015/04/13/17/45/icon-720944_1280.png',
        alt: 'Youtube',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2013/02/12/09/07/microsoft-80658_1280.png',
        alt: 'Microsoft',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2015/04/23/17/41/node-js-736399_1280.png',
        alt: 'Node JS',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2015/10/31/12/54/google-1015751_1280.png',
        alt: 'Google',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2021/12/06/13/45/meta-6850393_1280.png',
        alt: 'Meta',
      },
      {
        src: 'https://cdn.pixabay.com/photo/2013/01/29/22/53/yahoo-76684_1280.png',
        alt: 'Yahoo',
      },
    ]}
  />

  <!-- Features2 Widget ************** -->

  <Features2
    title="What services do we provide?"
    subtitle="We offer a wide range of website templates that suit various industries and purposes such as business, portfolio, e-commerce, blog, etc."
    items={[
      {
        title: 'Installation Instructions',
        description:
          'Offer clear instructions on how to download the purchased templates and install them on various website platforms or content management systems.',
        icon: 'flat-color-icons:document',
      },
      {
        title: 'Demo and Previews',
        description:
          'Provide interactive demos and previews that allow customers to see how their chosen template will look and function before making a purchase.',
        icon: 'flat-color-icons:template',
      },
      {
        title: 'Technical Support',
        description:
          'Providing customer support for any technical issues related to the templates or their implementation.',
        icon: 'flat-color-icons:voice-presentation',
      },
    ]}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Features2>

  <!-- Features Widget *************** -->

  <Features
    id="features"
    title="Main features of our templates"
    subtitle="Possess several key characteristics to effectively cater to the needs of startups and entrepreneurs."
    columns={3}
    items={[
      {
        title: 'Modern and Professional Design',
        description:
          'Have a contemporary design that reflects current design trends and gives a professional impression.',
        icon: 'tabler:artboard',
      },
      {
        title: 'Responsive and Mobile-Friendly',
        description:
          'Adapt seamlessly to different screen sizes and devices to ensure a consistent experience.',
        icon: 'tabler:picture-in-picture',
      },
      {
        title: 'Customizability',
        description:
          'Easily customizable, allowing users to adapt the design, colors, typography, and content to match their brand identity.',
        icon: 'tabler:adjustments-horizontal',
      },
      {
        title: 'Fast Loading Times',
        description:
          'Optimized for speed to ensure a smooth user experience and favorable search engine rankings.',
        icon: 'tabler:rocket',
      },
      {
        title: 'Search Engine Optimization (SEO)',
        description:
          'Incorporate SEO best practices in template structure and code to improve visibility in search engine results.',
        icon: 'tabler:arrows-right-left',
      },
      {
        title: 'Compatibility',
        description:
          'The templates work seamlessly across various content management systems and website builders.',
        icon: 'tabler:plug-connected',
      },
    ]}
  />

  <!-- FAQs Widget ******************* -->

  <FAQs
    title="Frequently Asked Questions"
    items={[
      {
        title: 'What are landing page templates?',
        description:
          'Landing page templates are pre-designed web page layouts that are specifically created to serve as a foundation for building effective landing pages. These templates are designed to capture the attention of visitors and guide them towards a specific action or goal, such as signing up for a newsletter, making a purchase, or downloading a resource.',
      },
      {
        title: 'Why should I use a template?',
        description:
          'Some of the advantages are that they provide a ready-to-use structure,  saving you significant time. Are designed with user-friendliness in mind and provide a cost-effective alternative, saving you money while still delivering a quality result.',
      },
      {
        title: 'Can I preview templates before buying?',
        description:
          'Yes, the templates allow you to preview them before making a purchase. There is a "Demo" button associated with each template.',
      },
      {
        title: 'Do I need technical skills to use a template?',
        description:
          'Advanced technical skills are not required to use a template, but having a basic understanding of web navigation and familiarity with using online tools can still be beneficial. If you have more specific customization needs, you might need to consult guides or reach out to customer support for assistance.',
      },
      {
        title: 'Can I use the template on multiple websites?',
        description:
          'No, the template comes with a single-use license, meaning you can use the template on one website or project only. Using the template on additional websites would require purchasing additional licenses.',
      },
      {
        title: 'What if I need help with customization?',
        description:
          "The templates provides a comprehensive step-by-step guide that walk you through the customization process. If you still have doubts, you can reach out to our customer support team. They can answer your questions, provide guidance on customization, and address any issues you're facing.",
      },
    ]}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </FAQs>

  <!-- Features3 Widget ************** -->

  <Features3
    title="Let us know how we can help"
    subtitle="We’re here to help and answer any question you might have."
    columns={4}
    items={[
      {
        title: 'Phone',
        icon: 'tabler:phone',
        callToAction: {
          target: '_blank',
          text: 'Call us',
          href: '/',
          variant: 'link',
        },
      },
      {
        title: 'Email',
        icon: 'tabler:mail',
        callToAction: {
          target: '_blank',
          text: 'Write to us',
          href: '/',
          variant: 'link',
        },
      },
      {
        title: 'Chat with sales',
        icon: 'tabler:message-circle',
        callToAction: {
          target: '_blank',
          text: 'Start chatting',
          href: '/',
          variant: 'link',
        },
      },
      {
        title: 'Chat with support',
        icon: 'tabler:message-circle',
        callToAction: {
          target: '_blank',
          text: 'Start chatting',
          href: '/',
          variant: 'link',
        },
      },
    ]}
  />

  <!-- CallToAction Widget *********** -->

  <CallToAction
    actions={[
      {
        variant: 'primary',
        target: '_blank',
        text: 'Get templates',
        href: 'https://github.com/onwidget/astrowind',
        icon: 'tabler:download',
      },
    ]}
  >
    <Fragment slot="title">Be a part of our vision</Fragment>

    <Fragment slot="subtitle">
      Discover a dynamic work environment, unparalleled growth opportunities, and the
      chance to make a meaningful impact.
    </Fragment>
  </CallToAction>
</Layout>
