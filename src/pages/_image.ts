import type { APIRoute } from 'astro';
import { getImage } from 'astro:assets';

export const GET: APIRoute = async ({ request }) => {
  const url = new URL(request.url);
  const params = url.searchParams;

  // Parse URL parameters (mimicking parseURL)
  const src = params.get('src');
  const width = params.has('w') ? parseInt(params.get('w')!) : undefined;
  const height = params.has('h') ? parseInt(params.get('h')!) : undefined;
  const format = params.get('f') as 'jpeg' | 'png' | 'webp' | 'gif' | 'avif' | undefined;
  const quality = params.has('q') ? parseInt(params.get('q')!) : undefined;
  const optimizeOptions = {
    src,
    width,
    height,
    format,
    quality,
  }

  if (!src || src.trim() === '') {
    return new Response('Missing or empty src parameter', { status: 400 });
  }

  // Get the image using getImage (this will use the built-in Sharp service)
  let resolvedImage;
  try {
    resolvedImage = await getImage({ ...optimizeOptions, src: src as string });
  } catch (error) {
    console.error('Error resolving image:', error);
    if (error instanceof Error) {
      if (error.message.includes('Input file is missing')) {
        return new Response('Image not found', { status: 404 });
      } else if (error.message.includes('Invalid image format')) {
        return new Response('Invalid image format', { status: 400 });
      }
    }
    return new Response('Error resolving image', { status: 500 });
  }

  // Fetch the image data
  const imageResponse = await fetch(resolvedImage.src);
  if (!imageResponse.ok) {
    console.error('Error fetching image:', imageResponse.statusText);
    return new Response('Error fetching image', { status: imageResponse.status });
  }

  const imageData = await imageResponse.arrayBuffer();

  // Return the image data with appropriate headers
  return new Response(imageData, {
    status: 200,
    headers: {
      'Content-Type': `image/${resolvedImage.options.format}`,
      'Cache-Control': 'public, max-age=31536000, immutable', // Adjust caching as needed
    },
  });
};
