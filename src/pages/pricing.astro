---
import Layout from '~/layouts/PageLayout.astro';
import { pricingInfo } from '~/data/pricingData.js';
import PricingSection from '~/components/ui/PricingSection.astro';
import RateTable from '~/components/ui/RateTable.astro';
import InfoList from '~/components/ui/InfoList.astro';
import ZoneCard from '~/components/ui/ZoneCard.astro';
import Headline from '~/components/ui/Headline.astro';


const metadata = {
  title: pricingInfo.pageTitle,
  description: pricingInfo.pageSubtitle,
  canonical: Astro.url.href,
};
---

<Layout metadata={metadata}>
  <div class="container mx-auto px-4 py-8 md:py-12">
    <!-- Page Header -->
    <header class="text-center mb-10 md:mb-16">
      <Headline tag="h1" class="text-4xl md:text-5xl font-bold mb-3 text-gray-900 dark:text-white">
        {pricingInfo.pageTitle}
      </Headline>
      <p class="text-lg text-gray-600 dark:text-slate-400 mb-4">{pricingInfo.pageSubtitle}</p>
      <div class="text-md text-gray-700 dark:text-slate-300">
        <span>Phone: <a href={`tel:${pricingInfo.contactDetails.phone.replace(/\D/g, '')}`} class="hover:text-primary dark:hover:text-blue-400">{pricingInfo.contactDetails.phone}</a></span>
        <span class="mx-2">|</span>
        <span>Website: <a href={`http://${pricingInfo.contactDetails.website}`} target="_blank" rel="noopener noreferrer" class="hover:text-primary dark:hover:text-blue-400">{pricingInfo.contactDetails.website}</a></span>
      </div>
    </header>

    <!-- Service Areas & Zones -->
    <PricingSection title={pricingInfo.serviceAreas.title}>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {pricingInfo.serviceAreas.zones.map(zone => (
          <ZoneCard name={zone.name} description={zone.description} />
        ))}
      </div>
    </PricingSection>

    <!-- One-Way Transfer Rates -->
    <PricingSection title={pricingInfo.oneWayRates.title}>
      {pricingInfo.oneWayRates.vehicles.map(vehicle => (
        <RateTable vehicleName={vehicle.name} rates={vehicle.rates} />
      ))}
    </PricingSection>

    <!-- Hourly Charter Rates -->
    <PricingSection title={pricingInfo.hourlyRates.title}>
      <div class="text-center">
        <h3 class="text-2xl font-semibold mb-2 text-gray-800 dark:text-slate-200">{pricingInfo.hourlyRates.vehicle.name}</h3>
        <p class="text-xl text-gray-700 dark:text-slate-300">{pricingInfo.hourlyRates.vehicle.rate}</p>
        <p class="text-sm text-gray-500 dark:text-slate-400">{pricingInfo.hourlyRates.vehicle.minimum}</p>
      </div>
    </PricingSection>

    <!-- Additional Charges -->
    <PricingSection title={pricingInfo.additionalCharges.title}>
      <InfoList items={pricingInfo.additionalCharges.items} />
    </PricingSection>

    <!-- Cancellation Policy -->
    <PricingSection title={pricingInfo.cancellationPolicy.title}>
      <InfoList items={pricingInfo.cancellationPolicy.policies} />
    </PricingSection>

    <!-- Footer Slogan -->
    <section class="mt-12 py-10 bg-gray-100 dark:bg-slate-900 rounded-lg">
      <div class="max-w-4xl mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-semibold mb-3 text-gray-900 dark:text-white">{pricingInfo.pageFooter.mainText}</h2>
        <p class="text-lg text-gray-600 dark:text-slate-400">{pricingInfo.pageFooter.subText}</p>
      </div>
    </section>
  </div>
</Layout>