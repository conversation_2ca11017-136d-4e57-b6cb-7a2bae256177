---
import type { Post } from '~/types';

export interface Props {
  tags: Post['tags'];
  class?: string;
  title?: string | undefined;
  isCategory?: boolean;
}

const { tags = [], class: className = '', title = undefined } = Astro.props;
---

{
  tags && tags.length > 0 && (
    <div class={className}>
      {title && <span class="align-super font-semibold">{title}</span>}
      <ul class="flex flex-wrap gap-2 sm:gap-3">
        {tags.map((tag) => (
          <li class="inline-block">
            <a
              href={`/tags/${tag}`}
              class="inline-block rounded-lg px-2 py-1 text-sm font-semibold bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
            >
              {tag}
            </a>
          </li>
        ))}
      </ul>
    </div>
  )
}
