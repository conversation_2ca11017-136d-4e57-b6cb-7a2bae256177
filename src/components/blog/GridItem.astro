---
import { APP_BLOG } from '~/utils/config';
import type { Post } from '~/types';

import Image from '~/components/common/Image.astro';

import { findImage } from '~/utils/images';
import { getPermalink } from '~/utils/permalinks';

export interface Props {
  post: Post;
}

const { post } = Astro.props;
const image = await findImage(post.image);
---

<article class="mb-6 transition">
  <div class="relative md:h-64 bg-gray-400 dark:bg-slate-700 rounded shadow-lg mb-6">
    {
      image && (
        <a href={getPermalink(post.permalink, 'post')}>
          <Image
            src={image}
            class="w-full md:h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700"
            widths={[400, 900]}
            width={400}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post.title}
            loading="lazy"
            decoding="async"
          />
        </a>
      )
    }
  </div>
  <h3 class="mb-2 text-xl font-bold leading-tight sm:text-2xl font-heading">
    {
      !APP_BLOG?.post?.isEnabled ? (
        post.title
      ) : (
        <a
          href={getPermalink(post.permalink, 'post')}
          class="hover:text-primary dark:hover:text-blue-700  transition ease-in duration-200"
        >
          {post.title}
        </a>
      )
    }
  </h3>
  <p class="text-muted dark:text-slate-400 text-lg">{post.excerpt}</p>
</article>
