---
import FleetCard from '~/components/ui/FleetCard.astro';
import car1 from 'src/assets/fleet/suv2.png';
import car2 from 'src/assets/fleet/2021-denali-4-3-2.png';
import car3 from 'src/assets/fleet/suv_sand.png';
import car4 from 'src/assets/fleet/suv_new_square.jpg';
import car5 from 'src/assets/fleet/suv2_square_new.jpg';
import car6 from 'src/assets/fleet/suv_wide_new.jpg';
// New images from May 2025 update
import captainsChairs from 'src/assets/fleet/may-21-25-update/Captains Chairs.jpg';
import aerialView1 from 'src/assets/fleet/may-21-25-update/DJI_20250412_193453_582.jpg';
import aerialView2 from 'src/assets/fleet/may-21-25-update/DJI_20250412_193453_582_1.jpg';
import aerialView3 from 'src/assets/fleet/may-21-25-update/DJI_20250412_193557_275.jpg';
import yukonXL from 'src/assets/fleet/may-21-25-update/GMC Yukon XL .jpeg';
import interiorView1 from 'src/assets/fleet/may-21-25-update/IMG_0886.jpeg';
import interiorView2 from 'src/assets/fleet/may-21-25-update/IMG_2726.jpeg';
import mercedesBenz from 'src/assets/fleet/may-21-25-update/Mercedes Benz LA West Image.jpg';
import sprinterInterior from 'src/assets/fleet/may-21-25-update/Sprinter Blue Interior.jpeg';

import type { FleetCard as FleetCardType } from '~/components/ui/FleetCard.astro';

export interface Fleet {
  fleetCards?: Array<FleetCardType>;
}

// Directly creating the fleetCards array
const fleetCards: FleetCardType[] = [
  {
    altText: 'SUV with mountains and trees',
    make: 'GMC',
    model: 'Yukon',
    image: car1,
    position: 'right',
  },
  { altText: 'SUV GMC Yukon', make: 'GMC', model: 'Yukon', image: car3 },
  { altText: 'SUV GMC SNow', make: 'GMC', model: 'Yukon', image: car2 },
  { altText: 'SUV GMC Yukon', make: 'GMC', model: 'Yukon', image: car4 },
  { altText: 'SUV GMC Yukon', make: 'GMC', model: 'Yukon', image: car5 },
  { altText: 'SUV GMC Yukon', make: 'GMC', model: 'Yukon', image: car6 },

  // New fleet images from May 2025 update
  { altText: 'Luxury SUV with captain\'s chairs', make: 'GMC', model: 'Yukon', image: captainsChairs },
  { altText: 'Aerial view of GMC Yukon', make: 'GMC', model: 'Yukon', image: aerialView1 },
  { altText: 'Aerial view of GMC Yukon from different angle', make: 'GMC', model: 'Yukon', image: aerialView2 },
  { altText: 'Aerial drone shot of GMC Yukon', make: 'GMC', model: 'Yukon', image: aerialView3 },
  { altText: 'GMC Yukon XL exterior view', make: 'GMC', model: 'Yukon XL', image: yukonXL },
  { altText: 'Luxury vehicle interior view', make: 'GMC', model: 'Yukon', image: interiorView1 },
  { altText: 'Premium SUV interior details', make: 'GMC', model: 'Yukon', image: interiorView2 },
  { altText: 'Mercedes Benz luxury vehicle', make: 'Mercedes', model: 'Benz', image: mercedesBenz },
  { altText: 'Mercedes Sprinter with blue interior', make: 'Mercedes', model: 'Sprinter', image: sprinterInterior },
];
---

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <div
    class="fleet-group row-start-auto grid grid-cols-1 md:grid-cols-2 md:[&>*:nth-child(3n)]:col-span-2
    md:[&>*:nth-child(3n)]:h-[490px] lg:[&>*:nth-child(3n)]:col-span-1 lg:grid-cols-3
    gap-[20px] mb-6 lg:mt-7 justify-items-center"
  >
    {
      fleetCards.map((card) => (
        <FleetCard
          key={card.model}
          altText={card.altText}
          make={card.make}
          model={card.model}
          image={card.image}
          position={card.position}
          class="h-[384px] md:h-[360px] w-auto md:[&>*:nth(3n)]:h-[490px] md:w-auto lg:h-[521px] 1080:h-[490px] xl:h-[541px] lg:w-auto"
        />
      ))
    }
  </div>
</div>
