---
// Import necessary components and types
import type { Content } from '~/types';
import WidgetWrapper from '../ui/WidgetWrapper.astro';
import Image from '~/components/common/Image.astro';
import Button from '~/components/ui/Button.astro';
import ItemGrid from '../ui/ItemGrid.astro';

// Extracting props from Astro.props
const {
  title = await Astro.slots.render('title'),
  tagline,
  content = await Astro.slots.render('content'),
  callToAction = await Astro.slots.render('calltoAction'), // Can be a single object or an array
  items = [],
  columns,
  image = await Astro.slots.render('image'),
  isReversed = false,
  isAfterContent = false,
  id,
  alignment = 'left', // Default alignment is 'left'
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Content;
---

<WidgetWrapper
  id={id}
  isDark={isDark}
  containerClass={`max-w-7xl mx-4 lg:mx-auto ${
    isAfterContent ? 'pt-0 lg:pt-0 lg:pt-0' : ''
  } ${classes?.container ?? ''}`}
  bg={bg}
>
  <div class={`mx-auto max-w-7xl lg:px-8 ${image ? '' : `text-${alignment}`}`}>
    <div
      class={`lg:flex ${
        !image ? 'lg:flex-col' : isReversed ? 'lg:flex-row-reverse' : ''
      } lg:gap-16`}
    >
      <div class={`lg:basis-1/2 ${!image ? 'lg:basis-full' : ''}`}>
        {
          tagline && (
            <div
              class="mb-5 text-title-small md:text-title translate-x-full intersect:-translate-x-0 transition animate-delay-[1500ms] duration-1000 intersect-once"
              set:html={tagline}
            />
          )
        }
        {
          title && (
            <div
              class="text-subtitle-small md:text-subtitle mb-[20px] -translate-x-full intersect:translate-x-0 transition animate-delay-[1500ms] duration-1000 intersect-once"
              set:html={title}
            />
          )
        }
        {
          content && (
            <div
              class="text-content-small md:text-content  -translate-x-full intersect:translate-x-0 transition animate-delay-[1500ms] duration-1000 intersect-once"
              set:html={content}
            />
          )
        }

        {
          callToAction && (
            <div class={`mb-8 pt-8 text-primary flex justify-${alignment} gap-4`}>
              {Array.isArray(callToAction)
                ? callToAction.map((cta, index) => (
                    <Button
                      key={index}
                      variant="link"
                      {...(typeof cta === 'object' ? cta : {})}
                    />
                  ))
                : typeof callToAction === 'object' && (
                    <Button
                      variant="link"
                      {...callToAction}
                    />
                  )}
            </div>
          )
        }
        <ItemGrid
          items={items}
          columns={columns}
          defaultIcon="tabler:map"
          classes={{
            container: `gap-y-4 lg:gap-y-8`,
            panel: 'max-w-none',
            title:
              'text-lg font-medium leading-6 dark:text-primary ml-2 rtl:ml-0 rtl:mr-2',
            description: 'text-muted dark:text-white ml-2 rtl:ml-0 rtl:mr-2',
            icon: 'flex h-7 w-7 items-center justify-center rounded-full bg-primary dark:bg-primary text-white p-1',
            action:
              'text-lg font-medium leading-6 dark:text-white ml-2 rtl:ml-0 rtl:mr-2',
          }}
        />
      </div>
      {
        image && (
          <div
            aria-hidden="true"
            class="mt-10 lg:mt-0 lg:basis-1/2 flex items-center justify-center"
          >
            <div class="relative mx-4">
              <div class="absolute inset-0 bg-black opacity-30 rounded-sm shadow-lg z-10" />
              {typeof image === 'string' ? (
                <Fragment set:html={image} />
              ) : (
                <Image
                  class="mx-auto max-w-3xl h-auto rounded-md bg-black shadow-lg "
                  width={500}
                  height={500}
                  layout="responsive"
                  aspectRatio={1 / 1}
                  {...(image as any)}
                />
              )}
            </div>
          </div>
        )
      }
    </div>
  </div>
</WidgetWrapper>
