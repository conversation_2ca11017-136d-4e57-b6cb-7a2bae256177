---
import <PERSON><PERSON> from '~/components/ui/Button.astro';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  content = await Astro.slots.render('content'),
  callToAction = await Astro.slots.render('callToAction'),
  callToAction2 = await Astro.slots.render('callToAction2'),
} = Astro.props;
---

<section class="relative md:-mt-[76px] not-prose">
  <div
    class="absolute inset-0 pointer-events-none"
    aria-hidden="true"
  >
  </div>
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6">
    <div class="pt-0 md:pt-[76px] pointer-events-none"></div>
    <div class="py-12 md:py-20 pb-8 md:pb-8">
      <div class="text-center max-w-5xl mx-auto">
        {
          title && (
            <h1
              class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-4 font-heading dark:text-gray-200"
              set:html={title}
            />
          )
        }
        {
          tagline && (
            <p
              class="text-tagline center mx-auto max-w-3xl "
              set:html={tagline}
            />
          )
        }

        <div class="max-w-xl mx-auto">
          {
            subtitle && (
              <p
                class="text-subtitle"
                set:html={subtitle}
              />
            )
          }
          <div
            class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4"
          >
            {
              callToAction && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction === 'string' ? (
                    <Fragment set:html={callToAction} />
                  ) : (
                    <Button
                      variant="primary"
                      {...callToAction}
                    />
                  )}
                </div>
              )
            }
            {
              callToAction2 && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction2 === 'string' ? (
                    <Fragment set:html={callToAction2} />
                  ) : (
                    <Button {...callToAction2} />
                  )}
                </div>
              )
            }
          </div>
        </div>
        {content && <Fragment set:html={content} />}
      </div>
    </div>
  </div>
</section>
