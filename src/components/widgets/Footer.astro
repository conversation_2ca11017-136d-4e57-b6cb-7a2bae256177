---
import { Icon } from 'astro-icon/components';
import { Image } from 'astro:assets';
import { SITE } from '~/utils/config';
import { getHomePermalink } from '~/utils/permalinks';
import nla from '~/assets/images/NLA.jpg';

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

interface Links {
  title?: string;
  links: Array<Link>;
}

export interface Props {
  links: Array<Links>;
  secondaryLinks: Array<Link>;
  socialLinks: Array<Link>;
  footNote?: string;
  theme?: string;
}

const {
  socialLinks = [],
  secondaryLinks = [],
  links = [],
  footNote = '',
  theme = 'light',
} = Astro.props;
---

<footer
  class:list={[
    { dark: theme === 'dark' },
    'relative border-t border-gray-200 dark:border-slate-800 not-prose',
  ]}
>
  <div
    class="dark:bg-dark absolute inset-0 pointer-events-none"
    aria-hidden="true"
  >
  </div>
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 dark:text-slate-300">
    <div
      class="grid grid-cols-12 md:flex md:flex-col xl:flex-row gap-4 gap-y-8 sm:gap-8 py-8 md:py-12"
    >
      <div class="col-span-12 lg:col-span-4">
        <div class="mb-2">
          <a
            class="inline-block font-bold text-xl"
            href={getHomePermalink()}
          >{SITE?.name}</a>
        </div>
        <div class="text-sm text-muted">
          {
            secondaryLinks.map(({ text, href }) => (
              <a
                class="text-muted hover:text-gray-700 dark:text-gray-400 decoration-[#59c0ff] hover:underline underline-offset-[13px] transition duration-150 ease-in-out mr-2 rtl:mr-0 rtl:ml-2"
                href={href}
              >{text}</a>
            ))
          }
        </div>
        <div class="text-muted text-sm mt-2">Luxury Limousine Permit LL-04310</div>
        <Image
          class="mt-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
          src={nla}
          alt="Luxury Limousine Permit NO LL-04310"
          width="200"
        />
      </div>
      {
        links.map(({ title, links }) => (
          <div class="col-span-6 md:col-span-3 lg:col-span-2">
            <div class="dark:text-gray-300 font-medium mb-2">{title}</div>
            {links && Array.isArray(links) && links.length > 0 && (
              <ul class="text-sm md:flex md:flex-row md:gap-x-4">
                {links.map(({ text, href, ariaLabel }) => (
                  <li class="mb-2 text-muted py-4 hover:text-gray-700 hover:underline decoration-[#59c0ff] underline-offset-[13px] dark:text-gray-400 transition duration-150 ease-in-out">
                    <a
                      class=""
                      href={href}
                      aria-label={ariaLabel}
                    >{text}</a>
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))
      }
    </div>
    <div class="md:flex md:items-center md:justify-between py-6 md:py-8">
      {
        socialLinks?.length ? (
          <ul class="flex mb-4 md:order-1 -ml-2 md:ml-4 md:mb-0 rtl:ml-0 rtl:-mr-2 rtl:md:ml-0 rtl:md:mr-4">
            {socialLinks.map(({ ariaLabel, href, text, icon }) => (
              <li>
                <a
                  class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
                  aria-label={ariaLabel}
                  href={href}
                >
                  {icon && (
                    <Icon
                      name={icon}
                      class="w-5 h-5"
                    />
                  )}
                  {text}
                </a>
              </li>
            ))}
          </ul>
        ) : (
          ''
        )
      }
      <div class="text-sm mr-4 dark:text-slate-400">{footNote}</div>
    </div>
  </div>
</footer>
