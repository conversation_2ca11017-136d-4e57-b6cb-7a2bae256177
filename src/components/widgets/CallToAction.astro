---
import WidgetWrapper from '../ui/WidgetWrapper.astro';
import type { CallToAction, Widget } from '~/types';
import Button from '~/components/ui/Button.astro';

interface Props extends Widget {
  title?: string;
  subtitle?: string;
  tagline?: string;
  href?: string;
  callToAction?: CallToAction;
  actions?: string | CallToAction[];
}

// Extract props using Astro.props
const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),
  actions = await Astro.slots.render('actions'),
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Props;
---

<WidgetWrapper
  id={id}
  isDark={isDark}
  containerClass={`w-auto mx-[20px] lg:p-4 pt-0 lg:mx-auto max-w-[1200px] ${
    classes?.container ?? ''
  }`}
  bg={bg}
>
  <div
    class="grid grid-cols-1 lg:grid-cols-2 gap-y-5 lg:gap-10 lg:mt-10 text-center lg:text-left"
  >
    <div class="left-column gap-y-5 flex flex-col lg:justify-end lg:items-start">
      {
        title && (
          <div
            class="title"
            set:html={title}
          />
        )
      }
      {
        subtitle && (
          <div
            class="subtitle"
            set:html={subtitle}
          />
        )
      }
    </div>
    <div class="right-column flex flex-col justify-between">
      <div class="actions xs:mb-0 mb-5">
        {
          actions && (
            <div class="flex flex-col justify-start items-center md:items-start gap-4">
              {Array.isArray(actions) ? (
                actions.map((action) => (
                  <div class="flex">
                    <Button
                      {...(action || {})}
                      class="w-full"
                    />
                  </div>
                ))
              ) : (
                <Fragment set:html={actions} />
              )}
            </div>
          )
        }
      </div>
      {
        tagline && (
          <div
            class="tagline mt-8 text-left mx-auto px-4 md:px-0 lg:px-0"
            set:html={tagline}
          />
        )
      }
    </div>
  </div>
</WidgetWrapper>
