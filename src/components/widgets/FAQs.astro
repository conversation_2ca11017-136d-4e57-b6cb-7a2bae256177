---
import Headline from '~/components/ui/Headline.astro';
import ItemGrid from '~/components/ui/ItemGrid.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { Faqs } from '~/types';

const {
  title = '',
  subtitle = '',
  tagline = '',
  items = [],
  columns = 2,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Faqs;
---

<WidgetWrapper
  id={id}
  isDark={isDark}
  containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`}
  bg={bg}
>
  <Headline
    title={title}
    subtitle={subtitle}
    tagline={tagline}
  />
  <ItemGrid
    items={items}
    columns={columns}
    defaultIcon="tabler:chevron-right"
    classes={{
      panel: 'max-w-none',
      container: `gap-y-4 lg:gap-y-8`,

      title: 'text-lg font-medium leading-6 dark:text-primary ml-2 rtl:ml-0 rtl:mr-2',
      description: 'text-muted dark:text-white ml-2 rtl:ml-0 rtl:mr-2',
      icon: 'flex h-7 w-7 items-center justify-center rounded-full bg-primary dark:bg-primary text-white p-1',
      action: 'text-lg font-medium leading-6 dark:text-white ml-2 rtl:ml-0 rtl:mr-2',
    }}
  />
</WidgetWrapper>
