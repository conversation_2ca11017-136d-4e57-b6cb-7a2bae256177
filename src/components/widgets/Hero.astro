---
//import Image from '~/components/common/Image.astro';
import Button from '~/components/ui/Button.astro';
import type { CallToAction } from '~/types';

export interface Props {
  id?: string;
  title?: string;
  subtitle?: string;
  tagline?: string;
  content?: string;
  actions?: string | CallToAction[];
  image?: string | any; // TODO: find HTMLElementProps
  videoSrc?: string; // New prop for video source URL
  isDark?: boolean;
}

const {
  id,
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  //tagline,
  content = await Astro.slots.render('content'),
  actions = await Astro.slots.render('actions'),
  //image = await Astro.slots.render('image'),
  videoSrc = await Astro.slots.render('videoSrc'),// Default video path
  isDark = false
} = Astro.props;
---

<section  class="relative h-full flex flex-col items-center text-center pl-[10px] pt-[155px] lg:pt-[325px] pb-[80px] bg-black" {...id ? { id } : {}}>
  {videoSrc && (
    <div class="video-docker absolute inset-0 w-full overflow-hidden z-0">
      <video class="min-w-full min-h-full absolute object-cover"
        src={videoSrc}
        autoplay muted loop playsinline />
      <div class:list={['absolute inset-0 bg-black bg-opacity-30', { [`bg-opacity-70`]: isDark }]}>
        <!-- Dynamic class for background opacity -->
      </div>
    </div>
  )}
  <div class=" relative z-10 flex flex-col items-center max-w-[1200px] md:items-start justify-center w-full px-4 md:pr-[180px] ">
    {title && (
      <p class="animate-fade-right animate-duration-[2500ms] -translate-x-full animate-once animate-ease-in-out animate-delay-300 text-title mb-5"
        set:html={title} />
    )}
    {subtitle && (
      <p class="animate-fade-right animate-duration-[2500ms] -translate-x-full animate-once animate-ease-in-out text-hero md:text-hero-lg  text-center md:text-left mb-[20px] max-w-5xl" 
        set:html={subtitle} />
    )}
    {content && (
      <p class="animate-fade-right animate-duration-[2500ms] translate-x-full animate-once animate-ease-in-out text-hero-content lg:text-hero-content-lg mb-[74px] text-left  "
        set:html={content} />
    )}
   
    
    {actions && (
      <div class="flex justify-center gap-4">
        {Array.isArray(actions) ? (
          actions.map((action) => (
            <Button {...(action || {})} class="text-base leading-4 border-[3px]  md:text-[26px] md:leading-[26px] uppercase md:px-[30px] md:py-[15px] hover:bg-white border-white hover:text-black hover:border-white" 
             />
          ))
        ) : (
          <Fragment set:html={actions} />
        )}
      </div>
    )}
   
  </div>
</section>

<style>


</style>