---
// This is a wrapper component for the astro-icon Icon component
// to work around TypeScript issues
import { Icon as AstroIcon } from 'astro-icon/components';

export interface Props {
  name: string;
  pack?: string;
  title?: string;
  class?: string;
  optimize?: boolean;
  width?: string | number;
  height?: string | number;
  stroke?: string;
  fill?: string;
  size?: string | number;
  [key: string]: any;
}

const props = Astro.props;
---

<AstroIcon {...props} />
