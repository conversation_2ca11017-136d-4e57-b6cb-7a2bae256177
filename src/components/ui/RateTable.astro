---
interface Rate {
  zone: string;
  price: string;
}

interface Props {
  vehicleName: string;
  rates: Rate[];
}

const { vehicleName, rates } = Astro.props;
---

<div class="mb-8 last:mb-0">
  <h3 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-slate-200">{vehicleName}</h3>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
      <thead class="bg-gray-50 dark:bg-slate-700">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">
            Zone
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-slate-300 uppercase tracking-wider">
            Rate
          </th>
        </tr>
      </thead>
      <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
        {rates.map((rate) => (
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
              {rate.zone}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-slate-300">
              {rate.price}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div>