---
import { twMerge } from 'tailwind-merge';
import Background from './Background.astro';

const { id, isDark = false, containerClass = '', bg, as = 'section' } = Astro.props;

const WrapperTag = as;
---

<WrapperTag
  class="relative not-prose scroll-mt-[72px]"
  {...id ? { id } : {}}
>
  <div
    class="absolute inset-0 pointer-events-none -z-[1]"
    aria-hidden="true"
  >
    <slot name="bg">
      {bg ? <Fragment set:html={bg} /> : <Background isDark={isDark} />}
    </slot>
  </div>
  <div
    class:list={[
      twMerge('relative mx-auto  py-4  text-default', containerClass),
      { dark: isDark },
    ]}
  >
    <slot />
  </div>
</WrapperTag>
