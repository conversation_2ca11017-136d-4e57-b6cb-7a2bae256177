---
interface Props {
  title: string;
  classes?: string;
}

const { title, classes = 'mb-12' } = Astro.props;
---

<section class:list={['py-8 md:py-10', classes]}>
  <div class="max-w-4xl mx-auto">
    {title && (
      <h2 class="text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
        {title}
      </h2>
    )}
    <div class="bg-white dark:bg-slate-800 shadow-lg rounded-lg p-6 md:p-8">
      <slot />
    </div>
  </div>
</section>