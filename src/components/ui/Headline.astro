---
import type { Headline } from '~/types';
const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  classes = {},
} = Astro.props as Headline;
const {
  container: containerClass = 'max-w-4xl',
  title: titleClass = 'text-title-small md:text-title text-left',
  subtitle: subtitleClass = 'text-subtitle-small text-left',
} = classes;
---
{
  (title || subtitle || tagline) && (
    <div class:list={['mb-8 md:mx-auto md:mb-12', containerClass]}>
      {title && (
        <h3 class:list={['text-3xl md:text-4xl font-bold', titleClass]}>{title}</h3>
      )}
      {tagline && (
        <p
          class="text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase"
        >{tagline}</p>
      )}
      {subtitle && (
        <p
          class:list={['mt-4 text-muted text-base md:text-lg', subtitleClass]}
        >{subtitle}</p>
      )}
    </div>
  )
}
