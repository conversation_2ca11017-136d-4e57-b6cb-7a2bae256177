---
//import { Icon } from "astro-icon/components";
//import { twMerge } from 'tailwind-merge';
import type { ItemGrid } from '~/types';
import Button from './Button.astro';
import { Image } from 'astro:assets';

const { items = [], columns, classes = {} } = Astro.props as ItemGrid;

const {
  container: containerClass = '',
  // container: containerClass = "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  panel: panelClass = '',
  title: titleClass = '',
  description: descriptionClass = '',
  //image: defaultImageClass = "text-primary",
} = classes;
---

{
  items && (
    <div
      class:list={[
        `grid gap-8 gap-x-12 sm:gap-y-8`,
        {
          'lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2': columns === 4,
          'lg:grid-cols-3 md:grid-cols-2': columns === 3,
          'sm:grid-cols-2': columns === 2,
        },
        containerClass,
      ]}
    >
      {items.map(
        ({ title, description, image, callToAction, classes: itemClasses = {} }) => (
          <div class:list={['relative flex flex-col', panelClass, itemClasses?.panel]}>
            {image && (
              <div class="relative m-auto max-w-4xl">
                {typeof image === 'string' ? (
                  <Image
                    class="mx-auto w-full rounded-lg bg-gray-500 shadow-lg"
                    src={image}
                    alt={title || ''}
                    width={500}
                    height={500}
                  />
                ) : (
                  <Image
                    class="mx-auto w-full rounded-lg bg-gray-500 shadow-lg"
                    {...(image as any)}
                  />
                )}
              </div>
            )}
            <div class:list={['text-xl font-bold', titleClass, itemClasses?.title]}>
              {title}
            </div>
            {description && (
              <p
                class:list={[
                  'text-muted mt-2',
                  descriptionClass,
                  itemClasses?.description,
                ]}
              >
                {description}
              </p>
            )}
            {callToAction && (
              <div class="mt-2">
                <Button {...callToAction} />
              </div>
            )}
          </div>
        )
      )}
    </div>
  )
}
