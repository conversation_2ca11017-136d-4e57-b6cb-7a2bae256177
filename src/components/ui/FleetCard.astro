---
import { Image } from 'astro:assets';

// Define the FleetCard interface using the type of imported images
export interface FleetCard {
  altText: string;
  make: string;
  model: string;
  image: any; // Using 'any' for now to avoid type errors
  position?: 'left' | 'right' | 'center';
}

const { image, altText, position } = Astro.props as FleetCard;
---

<div
  class="w-full xs:h-[384px] md:h-[524px] md:[&>*:nth(3n)]:h-[490px] lg:h-[521px] bg-transparent scale-50 opacity-0 intersect:scale-100 intersect:opacity-100 transition odd:duration-300 even:duration-700 even:delay-100 odd:delay-200 intersect-once"
>
  <Image
    src={image}
    alt={altText}
    class:list={[
      'object-cover w-full h-full',
      {
        'object-left': position === 'left',
        'object-right': position === 'right',
        'object-center': position === 'center' || !position,
      },
    ]}
  />
  <div
    class="absolute z-50 inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition duration-500 ease-in-out"
  >
  </div>
</div>
<!-- Overlay div -->

<!-- Enhanced content div -->
<!-- <div
    class="card-content z-10 items-start bg-black bg-opacity-0 p-4 gap-y-3 flex justify-end absolute bottom-[-32px] flex-col transition-all duration-500 ease-in-out transform group-hover:translate-y-[-35px] group-hover:opacity-100"
  >
    <p class="text-card-title">{make}</p>
    <p class="text-white text-xl uppercase leading-6 tracking-normal md:text-[26px] lg:leading-8">{model}</p>
    <button
      class="group relative uppercase text-white font-medium rounded-md text-[11px] mt-6 pb-[2px] border-b-transparent border-b hover:border-b-white transition-all ease-in-out duration-500"
    >
      Book Now
      <span
        class="absolute bottom-0 left-0 w-0 h-[1px] bg-white transition-all duration-500 ease-in-out group-hover:w-full"
      ></span>
    </button>
  </div> -->
