// Type declaration for astro-icon components
// This helps TypeScript understand the astro-icon module exports

declare module 'astro-icon/components' {
  import type { HTMLAttributes } from 'astro/types';

  export interface IconProps extends HTMLAttributes<'svg'> {
    name: string;
    pack?: string;
    title?: string;
    class?: string;
    optimize?: boolean;
    width?: string | number;
    height?: string | number;
    stroke?: string;
    fill?: string;
    size?: string | number;
    [key: string]: any;
  }

  export const Icon: (props: IconProps) => any;
}
