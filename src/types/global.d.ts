declare module 'astro-icon/components' {
  import type { HTMLAttributes } from 'astro/types';

  interface IconProps extends HTMLAttributes<'svg'> {
    name: string;
    pack?: string;
    title?: string;
    class?: string;
    optimize?: boolean;
    width?: string | number;
    height?: string | number;
    stroke?: string;
    fill?: string;
    size?: string | number;
  }

  export const Icon: (props: IconProps) => any;
}
