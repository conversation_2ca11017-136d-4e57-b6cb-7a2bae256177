# Astro v5 Features Overview

Astro v5 offers a range of powerful features designed to help you build fast, content-focused websites. This guide provides an overview of Astro's key features.

## Islands Architecture

Astro's Islands architecture is a core concept that enables you to build websites with minimal JavaScript. Islands are interactive components that are hydrated on the client-side, while the rest of the page remains static HTML. This approach significantly improves performance by reducing the amount of JavaScript that needs to be loaded and executed.

[Learn more about Islands architecture](/en/concepts/islands/)

## Astro Components

Astro components are the building blocks of Astro projects. They are similar to components in other frameworks like React or Vue, but they are rendered to static HTML by default. Astro components can be interactive by adding client-side scripts or by using Islands.

[Learn more about Astro components](/en/basics/astro-components/)

## Astro Template Syntax

Astro's template syntax is a superset of HTML that allows you to use JavaScript expressions, conditional rendering, and loops within your templates. It's designed to be familiar and easy to learn for anyone who has worked with HTML or JSX.

[Learn more about the Astro template syntax](/en/reference/astro-syntax/)

## Content Collections

Astro's content collections feature provides a type-safe way to manage and query your content. You can define schemas for your content, ensuring that your data is consistent and valid. Content collections are particularly useful for blogs, documentation sites, and other content-heavy projects.

[Learn more about content collections](/en/guides/content-collections/)

## View Transitions

Astro v5 enhances support for view transitions, allowing you to create smooth and engaging navigation experiences. View transitions enable you to animate the transition between pages, providing a more dynamic and interactive feel to your website.

[Learn more about view transitions](/en/guides/view-transitions/)

## Integrations

Astro supports a wide range of integrations, allowing you to easily incorporate popular libraries and services into your projects. You can add integrations for frameworks like React, Vue, and Svelte, as well as for CSS tools like Tailwind CSS and Sass.

[Learn more about Astro integrations](/en/guides/integrations-guide/)

## Headless CMS Integration

Astro can be integrated with various headless CMS platforms, allowing you to manage your content separately from your codebase. This provides flexibility and scalability for your projects, enabling you to update content without redeploying your site.

[Learn more about integrating a headless CMS](/en/guides/cms/)

## Extending Astro

Astro is designed to be extensible, allowing you to customize and enhance its functionality to meet your specific needs. You can create your own integrations, plugins, and components to extend Astro's capabilities.
