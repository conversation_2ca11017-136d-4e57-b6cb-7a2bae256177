# Image Service API

This document outlines the key concepts and usage of Astro's Image Service API, which is used by `astro:assets` to facilitate integration with various image optimization services.

## Key Concepts

Astro supports two types of image services:

1. **Local Services:** Perform image transformations directly during build (for static sites) or at runtime (for development and on-demand rendering). Examples include wrappers around libraries like Sharp or ImageMagick.
2. **External Services:** Delegate image processing to remote services (e.g., Cloudinary, Vercel) by generating URLs that point to those services.

## Local Services

Local services are used in development mode and for on-demand rendering in production. They rely on an API endpoint to perform transformations. Astro provides a built-in endpoint (`/_image`), but you can create your own.

### Endpoint

The endpoint receives a request and returns the transformed image. The default endpoint is `/_image`.

### `getURL()`

This method constructs the URL for the image endpoint, including query parameters for image transformations.

**Example:**

```typescript
getURL(options: ImageTransform, imageConfig: AstroConfig['image']) {
  const searchParams = new URLSearchParams();
  searchParams.append('href', typeof options.src === "string" ? options.src : options.src.src);
  options.width && searchParams.append('w', options.width.toString());
  options.height && searchParams.append('h', options.height.toString());
  options.quality && searchParams.append('q', options.quality.toString());
  options.format && searchParams.append('f', options.format);
  return `/my_custom_endpoint_that_transforms_images?${searchParams}`;
  // Or use the built-in endpoint:
  // return `/_image?${searchParams}`;
}
```

### `parseURL()`

This method parses the URL generated by `getURL()` to extract the transformation options.

**Example:**

```typescript
parseURL(url: URL, imageConfig) {
  return {
    src: params.get('href')!,
    width: params.has('w') ? parseInt(params.get('w')!) : undefined,
    height: params.has('h') ? parseInt(params.get('h')!) : undefined,
    format: params.get('f'),
    quality: params.get('q'),
  };
}
```

### `transform()`

This method performs the actual image transformation using a library like Sharp.

**Example:**

```typescript
transform(buffer: Uint8Array, options: { src: string, [key: string]: any }, imageConfig): {  Uint8Array, format: OutputFormat } {
  const { buffer } = mySuperLibraryThatEncodesImages(options);
  return {
     buffer,
    format: options.format,
  };
}
```

### `getHTMLAttributes()`

This method generates HTML attributes for the `<img>` tag, such as `width`, `height`, `loading`, and `decoding`.

**Example:**

```typescript
getHTMLAttributes(options, imageConfig) {
  let targetWidth = options.width;
  let targetHeight = options.height;
  if (typeof options.src === "object") {
    const aspectRatio = options.src.width / options.src.height;

    if (targetHeight && !targetWidth) {
      targetWidth = Math.round(targetHeight * aspectRatio);
    } else if (targetWidth && !targetHeight) {
      targetHeight = Math.round(targetWidth / aspectRatio);
    }
  }

  const { src, width, height, format, quality, ...attributes } = options;

  return {
    ...attributes,
    width: targetWidth,
    height: targetHeight,
    loading: attributes.loading ?? 'lazy',
    decoding: attributes.decoding ?? 'async',
  };
}
```

## External Services

External services generate a URL that points to an external image processing service.

### `getURL()`

This method constructs the URL for the external image service, including query parameters for image transformations.

**Example:**

```typescript
getURL(options, imageConfig) {
  return `https://mysupercdn.com/${options.src}?q=${options.quality}&w=${options.width}&h=${options.height}`;
}
```

### `getHTMLAttributes()`

This method generates HTML attributes for the `<img>` tag.

**Example:**

```typescript
getHTMLAttributes(options, imageConfig) {
  const { src, format, quality, ...attributes } = options;
  return {
    ...attributes,
    loading: options.loading ?? 'lazy',
    decoding: options.decoding ?? 'async',
  };
}
```

## The `/_image` Endpoint Issue

The warning message "No API Route handler exists for the method 'GET' for the route '/_image'. Found handlers: 'get'" indicates that the built-in `/_image` endpoint in Astro is defined with a lowercase "get" handler instead of an uppercase "GET" handler. This is a case-sensitivity issue.

**To resolve this, we need to ensure that the API route handler for `/_image` is correctly defined as `GET`.**
```
