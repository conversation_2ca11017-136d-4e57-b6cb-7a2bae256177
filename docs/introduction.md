# Introduction to Astro v5

Astro v5 is the latest version of the Astro framework, designed for building fast, content-focused websites. It introduces several new features and improvements over previous versions, enhancing performance, developer experience, and flexibility.

## Key Features of Astro v5

-   **Improved Performance:** Astro v5 continues the tradition of delivering lightning-fast websites by optimizing the way components are rendered and hydrated.
-   **Enhanced Developer Experience:** With updates to the Astro compiler and tooling, developers can enjoy a smoother and more intuitive development process.
-   **New Integrations:** Astro v5 expands its ecosystem with new integrations, making it easier to incorporate popular libraries and services into your projects.
-   **Advanced Content Collections:** Building on the existing content collections feature, Astro v5 offers more powerful ways to manage and query your content.
-   **Enhanced View Transitions:** Astro v5 provides enhanced support for view transitions, allowing for smoother and more engaging navigation experiences.

## Why Choose Astro?

Astro's unique Islands architecture allows you to build websites with minimal JavaScript, ensuring faster load times and better performance. It's an ideal choice for content-heavy sites like blogs, portfolios, documentation sites, and e-commerce platforms.

## Getting Started

To start using Astro v5, you can install it via npm, yarn, or pnpm. Follow the [Installation Guide](/docs/installation.md) for detailed instructions.

If you're upgrading from a previous version of Astro, refer to the [Upgrade Guide](/docs/upgrade.md) for a smooth transition.

## Learn More

Explore the following resources to dive deeper into Astro v5:

-   [Features Overview](/docs/features/)
-   [Extending Astro](/docs/extending/)
-   [Astro Community](https://astro.build/chat)
