# Astro v5 Guide

This document summarizes the key aspects of Astro v5, based on the [Astro documentation](https://docs.astro.build/en/getting-started/).

## Installation

Astro can be installed using the following command:

```bash
npm create astro@latest
```

The installation guide provides step-by-step instructions for installing Astro using the CLI wizard, creating a new project from an existing Astro GitHub repository, and installing Astro manually.

## Key Features

-   **Astro's main features**: Overview of Astro's capabilities.
-   **Islands architecture**: Explanation of Astro's Islands architecture.
-   **Astro components**: Introduction to Astro components.
-   **Astro template syntax**: Reference for Astro's template syntax.

## Extending Astro

-   **Integrations**: Add integrations like React and Tailwind.
-   **Content Collections**: Create type-safe content collections.
-   **View Transitions**: Enhance navigation with view transitions.
-   **CMS Integration**: Connect a headless CMS to your project.

## Getting Involved

Join the [Astro Discord community](https://astro.build/chat) to ask questions and get involved.

## Contributing

You can contribute to As<PERSON> by providing feedback or creating issues on [GitHub](https://github.com/withastro/docs/issues/new).

## Feedback

Send feedback directly to the Astro team or create a GitHub issue for quicker response.

For more detailed information, refer to the official [Astro documentation](https://docs.astro.build/en/getting-started/).
