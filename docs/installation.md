# Installing Astro v5

This guide provides step-by-step instructions for installing Astro v5. You can install Astro using our CLI wizard, create a new project from an existing Astro GitHub repository, or install Astro manually.

## Using the Astro CLI Wizard

The easiest way to install Astro is by using the `create-astro` CLI wizard. Follow these steps:

1. Open your terminal.
2. Run one of the following commands:

    ```bash
    # Using npm
    npm create astro@latest

    # Using yarn
    yarn create astro

    # Using pnpm
    pnpm create astro@latest
    ```

3. Follow the prompts in the wizard to configure your new Astro project.

## Creating a Project from a GitHub Repository

If you prefer to start with an existing Astro project, you can clone a repository from GitHub:

1. Navigate to the Astro project repository on GitHub.
2. Click the "Use this template" button to create a new repository based on the project.
3. Clone your newly created repository to your local machine:

    ```bash
    git clone <your-repository-url>
    ```

4. Install the project's dependencies:

    ```bash
    # Using npm
    npm install

    # Using yarn
    yarn

    # Using pnpm
    pnpm install
    ```

## Manual Installation

For manual installation, follow these steps:

1. Create a new directory for your project:

    ```bash
    mkdir my-astro-project
    cd my-astro-project
    ```

2. Initialize a new npm project:

    ```bash
    npm init -y
    ```

3. Install Astro as a dependency:

    ```bash
    # Using npm
    npm install astro

    # Using yarn
    yarn add astro

    # Using pnpm
    pnpm add astro
    ```

4. Create an `astro.config.mjs` file in the root of your project.
5. Create a `src/pages` directory and add your first Astro page (e.g., `index.astro`).

## Next Steps

Once you have installed Astro, you can start building your website. Refer to the [Introduction to Astro v5](/docs/introduction.md) for an overview of Astro's features and capabilities.
