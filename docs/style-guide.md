# Rad Transport Style Guide

## 1. Brand Identity

### Colors
- **Primary**: `rgb(101, 190, 230)` - Light blue, used for primary actions and emphasis
- **Secondary**: `rgb(30, 58, 138)` - Dark blue, used for secondary elements
- **Accent**: `rgb(109, 40, 217)` - Purple, used sparingly for highlighting important elements
- **Text Colors**:
  - Default: `rgb(229, 236, 246)` (dark mode) / `rgb(16, 16, 16)` (light mode)
  - Muted: 66% opacity of default text
  - Headings: Same as default text for better readability

### Typography
- **Primary Font**: 'Raleway Variable' for all text including headings
- **Button Font**: 'Times New Roman' for a distinctive button style
- **Letter Spacing**: 1.3px for improved readability
- **Font Sizes**:
  - Headings: 
    - H1: `text-4xl md:text-5xl font-bold` (2.25rem/3rem)
    - H2: `text-3xl md:text-4xl font-bold` (1.875rem/2.25rem)
    - H3: `text-2xl md:text-3xl font-bold` (1.5rem/1.875rem)
  - Body: `text-base md:text-lg` (1rem/1.125rem)
  - Small/Caption: `text-sm` (0.875rem)

## 2. Layout & Spacing

### Breakpoints
- **xxs**: 320px
- **xs**: 480px
- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px
- **3xl**: 1920px
- **Custom**: 1080px

### Container Widths
- **Default**: `max-w-4xl` (56rem)
- **Narrow**: `max-w-2xl` (42rem)
- **Wide**: `max-w-6xl` (72rem)
- **Full**: `max-w-full`

### Spacing
- Use Tailwind's spacing scale consistently:
  - Vertical spacing between sections: `my-8 md:my-12 lg:my-16`
  - Component padding: `p-4 md:p-6 lg:p-8`
  - Gap between grid items: `gap-4 md:gap-6`

## 3. Components

### Buttons
- **Variants**:
  - Primary: Blue background with white text
  - Secondary: Transparent with border
  - Tertiary: Text-only with hover effect
  - Link: Text with hover color change
- **Structure**: Include icon support with consistent spacing
- **States**: Include hover, focus, and active states
- **Size**: Maintain consistent padding and text size

### Cards
- **Structure**: Consistent padding, border-radius
- **Animation**: Use the defined intersect animation pattern
- **Transitions**: 
  - Duration: 300ms-700ms
  - Timing: Use cubic-bezier for smooth animations

### Typography Components
- **Headlines**: Consistent structure with title, subtitle, and tagline options
- **Text Blocks**: Maintain consistent spacing between paragraphs

## 4. Animations & Transitions

### Standard Animations
- **Fade In Left**:
  ```css
  @keyframes fadeInLeft {
    '0%': { opacity: '0', transform: 'translateX(-4rem)' },
    '100%': { opacity: '1', transform: 'translateX(0)' }
  }
  ```
- **Intersection Animations**: Use for elements that should animate when scrolled into view
  - Scale from 50% to 100%
  - Opacity from 0 to 100%
  - Transition duration: 300ms-700ms with staggered delays

### Hover Effects
- **Standard Transition**: `transition duration-300 ease-in-out`
- **Button Hover**: Color change with subtle scale
- **Card Hover**: Overlay with opacity change

## 5. Dark Mode

- **Default Theme**: Dark-only (`dark:only`)
- **Dark Mode Colors**:
  - Background: Black (`rgb(0, 0, 0)`)
  - Text: Light (`rgb(229, 236, 246)`)
  - Primary: Light blue (`rgb(101, 190, 230)`)
- **Selection Color**: Black background with snow text

## 6. Images & Media

- **Image Handling**:
  - Use Astro's Image component for optimization
  - Support for different object positions (left, right, center)
  - Consistent aspect ratios across breakpoints
- **Image Overlays**: Use consistent overlay styles with hover effects

## 7. Forms & Inputs

- **Input Styling**: Consistent border, padding, and focus states
- **Validation**: Clear error states and messaging
- **Button Alignment**: Consistent positioning and spacing

## 8. Accessibility

- **Color Contrast**: Ensure all text meets WCAG AA standards
- **Focus States**: Visible focus indicators for keyboard navigation
- **Text Sizing**: Maintain minimum readable text sizes
- **RTL Support**: Support for right-to-left languages with appropriate class modifiers

## 9. Code Conventions

### CSS/Tailwind
- Use Tailwind utility classes consistently
- Group related utilities together
- Use `twMerge` for conditional class application
- Follow responsive pattern: mobile-first with breakpoint modifiers

### Component Structure
- Consistent prop interfaces for all components
- Use TypeScript types for props
- Follow Astro's component patterns consistently

## 10. Page-Specific Patterns

### Tour Pages
- **Hero Section**:
  - Full-width hero: `w-screen h-[65vh] md:h-[75vh]`
  - Text overlay: Centered with shadow for readability
  - Gradient overlay for image: `bg-gradient-to-t from-black/70 via-black/40 to-black/10`
- **Content Layout**:
  - Container with consistent padding: `container mx-auto px-4`
  - Section spacing: `space-y-16 md:space-y-24`
  - Two-column layout for overview/details: `grid md:grid-cols-5 gap-8 md:gap-12`
- **Sticky Elements**:
  - Key details card: `sticky top-24`
- **Call to Action**:
  - Floating CTA button: `fixed bottom-[10px] right-[10px] left-[10px] md:bottom-5 md:right-5 md:left-auto z-50`
  - Multiple CTA options: Primary for booking, Secondary for inquiries

## 11. Implementation Guidelines

- Use this style guide as a reference for all new components
- When modifying existing components, update to match this guide
- Document any exceptions or special cases
- Review the guide periodically and update as the design evolves
- Follow responsive patterns consistently across all pages
