# Astro View Transitions Guide

## Overview
- Opt-in client-side page transitions
- Powered by browser's View Transitions API
- Built-in animations: `fade`, `slide`, `none`
- Supports `prefers-reduced-motion`
- Fallback support for non-supporting browsers

## Setup
1. Import `ClientRouter`:
   ```astro
   import { ClientRouter } from 'astro:transitions';
   ```
2. Add to `<head>` of each page:
   ```astro
   ```

## Transition Directives
Use on elements to control transitions:
- `transition:name="unique-name"` - Manually match elements
- `transition:animate="animation-type"` - Set animation
- `transition:persist` - Keep element state across navigation

## Built-in Animations
- `fade` (default): Crossfade animation
- `slide`: Slide content left/right
- `none`: Disable animations
- `initial`: Use browser default

Example:
```astro
<main transition:animate="slide">
```

## Custom Animations
1. Import animation functions:
   ```astro
   import { fade, slide } from 'astro:transitions';
   ```
2. Customize:
   ```astro
   <header transition:animate={fade({ duration: '0.4s' })}>
   ```

## Lifecycle Events
Listen for navigation events:
- `astro:before-preparation` - Before loading starts
- `astro:after-preparation` - After content loaded
- `astro:before-swap` - Before DOM swap
- `astro:after-swap` - After DOM swap
- `astro:page-load` - After page fully loaded

Example:
```astro
<script>
  document.addEventListener('astro:page-load', () => {
    // Setup after navigation
  });
</script>
```

## Best Practices
- Add `ClientRouter` to each page's `<head>`
- Use `transition:animate` on key elements
- Handle state persistence with `transition:persist`
- Use lifecycle events for post-navigation setup
- Always include `<title>` for accessibility
