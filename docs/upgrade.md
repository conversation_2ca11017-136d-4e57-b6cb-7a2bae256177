# Upgrading to Astro v5

This guide provides instructions on how to upgrade your existing Astro site to Astro v5.

## Checking Your Current Astro Version

Before upgrading, check your current Astro version by running the following command in your project's root directory:

```bash
npx astro --version
```

## Upgrading Astro

To upgrade to Astro v5, run one of the following commands in your project's root directory:

```bash
# Using npm
npm install astro@latest

# Using yarn
yarn add astro@latest

# Using pnpm
pnpm add astro@latest
```

This will update Astro and its dependencies to the latest versions.

## Updating Configuration

Astro v5 may introduce changes to the configuration file (`astro.config.mjs`). Review the [Astro v5 Configuration Reference](/docs/configuration/) to ensure your configuration is up-to-date.

## Handling Breaking Changes

Astro v5 may include breaking changes that require modifications to your existing codebase. Consult the [Astro v5 Changelog](/docs/changelog/) for a list of breaking changes and instructions on how to address them.

## Testing Your Upgraded Site

After upgrading, thoroughly test your site to ensure everything works as expected. Pay close attention to areas affected by breaking changes or configuration updates.

## Seeking Help

If you encounter any issues during the upgrade process, visit the [Astro Community](https://astro.build/chat) for assistance.
