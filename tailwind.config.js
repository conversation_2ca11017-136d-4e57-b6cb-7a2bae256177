const defaultTheme = require('tailwindcss/defaultTheme');
import intersect from 'tailwindcss-intersect';

module.exports = {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    screens: {
      xxs: '320px',
      xs: '480px',
      1080: '1080px',
      ...defaultTheme.screens,
    },
    extend: {
      screens: {
        '3xl': '1920px',
      },
      gridTemplateColumns: {
        'custom-3': 'repeat(3, minmax(0, 1fr))',
      },
      keyframes: {
        fadeInLeft: {
          '0%': { opacity: '0', transform: 'translateX(-4rem)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
      },
      animation: {
        'fade-in-left': 'fadeInLeft 3s cubic-bezier(0.23, 1, 0.32, 1) forwards',
      },

      colors: {
        primary: 'var(--aw-color-primary)',
        secondary: 'var(--aw-color-secondary)',
        accent: 'var(--aw-color-accent)',
        default: 'var(--aw-color-text-default)',
        muted: 'var(--aw-color-text-muted)',
      },
      fontFamily: {
        sans: ['var(--aw-font-sans)', ...defaultTheme.fontFamily.sans],
        serif: ['var(--aw-font-serif)', ...defaultTheme.fontFamily.serif],
        heading: ['var(--aw-font-heading)', ...defaultTheme.fontFamily.sans],
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwindcss-animated'),
    intersect
  ],
  darkMode: 'class',
};